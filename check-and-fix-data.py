#!/usr/bin/env python3
"""
数据检查和修复脚本
用于检查数据库中的数据并创建测试数据
"""

import sqlite3
import json
import os
from datetime import datetime

# 数据库文件路径
DB_PATH = "/opt/hypcup-event/data/app.db"
LOCAL_DB_PATH = "./data/app.db"

def get_db_path():
    """获取数据库路径"""
    if os.path.exists(DB_PATH):
        return DB_PATH
    elif os.path.exists(LOCAL_DB_PATH):
        return LOCAL_DB_PATH
    else:
        print(f"❌ 数据库文件不存在: {DB_PATH} 或 {LOCAL_DB_PATH}")
        return None

def check_database():
    """检查数据库状态"""
    db_path = get_db_path()
    if not db_path:
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"📊 数据库文件: {db_path}")
        print(f"📊 数据库大小: {os.path.getsize(db_path)} bytes")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 数据库表: {[table[0] for table in tables]}")
        
        # 检查作品数据
        if ('works',) in tables:
            cursor.execute("SELECT COUNT(*) FROM works")
            works_count = cursor.fetchone()[0]
            print(f"📊 作品数量: {works_count}")
            
            if works_count > 0:
                cursor.execute("SELECT id, title FROM works LIMIT 5")
                works = cursor.fetchall()
                print("📝 前5个作品:")
                for work in works:
                    print(f"   ID: {work[0]}, 标题: {work[1]}")
        
        # 检查用户数据
        if ('users',) in tables:
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            print(f"👥 用户数量: {users_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    db_path = get_db_path()
    if not db_path:
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有数据
        cursor.execute("SELECT COUNT(*) FROM works")
        works_count = cursor.fetchone()[0]
        
        if works_count > 0:
            print(f"ℹ️  数据库中已有 {works_count} 个作品，跳过创建测试数据")
            conn.close()
            return True
        
        print("🔧 创建测试数据...")
        
        # 创建测试作品数据
        test_works = [
            {
                'id': 63,
                'title': '现代建筑设计方案',
                'description': '这是一个创新的现代建筑设计方案，融合了可持续发展理念和现代美学。',
                'author': '张三',
                'school': '清华大学建筑学院',
                'year': 2024,
                'award': '一等奖',
                'image_url': '/uploads/works/sample1.jpg',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            },
            {
                'id': 64,
                'title': '生态友好住宅设计',
                'description': '注重环保和生态平衡的住宅设计，采用绿色建筑技术。',
                'author': '李四',
                'school': '同济大学建筑与城市规划学院',
                'year': 2024,
                'award': '二等奖',
                'image_url': '/uploads/works/sample2.jpg',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            },
            {
                'id': 65,
                'title': '城市更新项目',
                'description': '针对老城区改造的创新设计方案，平衡历史保护与现代发展。',
                'author': '王五',
                'school': '东南大学建筑学院',
                'year': 2024,
                'award': '三等奖',
                'image_url': '/uploads/works/sample3.jpg',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
        ]
        
        # 插入测试数据
        for work in test_works:
            cursor.execute("""
                INSERT OR REPLACE INTO works 
                (id, title, description, author, school, year, award, image_url, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                work['id'], work['title'], work['description'], work['author'],
                work['school'], work['year'], work['award'], work['image_url'],
                work['created_at'], work['updated_at']
            ))
        
        conn.commit()
        print(f"✅ 成功创建 {len(test_works)} 个测试作品")
        
        # 创建管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE email = ?", ('<EMAIL>',))
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # 这里需要根据实际的用户表结构来调整
            print("🔧 创建管理员用户...")
            # 注意：实际密码应该是哈希后的
            cursor.execute("""
                INSERT OR REPLACE INTO users 
                (email, username, hashed_password, is_admin, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                '<EMAIL>',
                'admin',
                '$2b$12$example_hashed_password',  # 这需要实际的哈希密码
                True,
                True,
                datetime.now().isoformat()
            ))
            conn.commit()
            print("✅ 管理员用户创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 数据库检查和修复工具")
    print("=" * 30)
    
    # 检查数据库
    if not check_database():
        print("❌ 数据库检查失败")
        return
    
    print("\n" + "=" * 30)
    
    # 创建测试数据
    if create_test_data():
        print("✅ 数据检查和修复完成")
    else:
        print("❌ 数据修复失败")
    
    print("\n🚀 建议的下一步:")
    print("1. 重启容器服务")
    print("2. 测试 API 端点: curl http://localhost:8000/api/works/")
    print("3. 测试前端页面: curl http://localhost:3000/work/63")
    print("4. 检查 Nginx 配置并重新加载")

if __name__ == "__main__":
    main()
