# Nginx 反向代理故障排除指南

## 🔍 问题描述

- ✅ `https://comp.uedmagazine.net/work/63` 可以正常访问
- ❌ `https://comp.uedmagazine.net` 返回 404

## 🎯 可能的原因

### 1. Nginx 配置问题

**最常见的问题**：Nginx 的 `location /` 规则配置不正确。

#### 检查方法：
```bash
# 检查 Nginx 配置语法
nginx -t

# 查看 Nginx 错误日志
tail -f /var/log/nginx/error.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

#### 正确的配置：
```nginx
# 确保 location / 在最后，且配置正确
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 2. 前端应用问题

#### 检查前端服务状态：
```bash
# 检查前端是否正常运行
curl -I http://localhost:3000/

# 检查容器状态
docker-compose ps

# 查看前端日志
docker-compose logs hypcup-event | grep frontend
```

### 3. 环境变量配置问题

#### 检查环境变量：
```bash
# 检查容器内环境变量
docker-compose exec hypcup-event env | grep NEXT_PUBLIC_API_URL

# 应该显示：NEXT_PUBLIC_API_URL=/api
```

## 🛠️ 解决步骤

### 步骤 1：验证服务状态

```bash
# 运行调试脚本
chmod +x debug-routing.sh
./debug-routing.sh
```

### 步骤 2：检查 Nginx 配置

1. **备份当前配置**：
```bash
cp /etc/nginx/sites-available/comp.uedmagazine.net /etc/nginx/sites-available/comp.uedmagazine.net.backup
```

2. **使用提供的配置**：
```bash
# 复制新配置
cp nginx-simple.conf /etc/nginx/sites-available/comp.uedmagazine.net

# 测试配置
nginx -t

# 重新加载配置
nginx -s reload
```

### 步骤 3：重新构建容器

```bash
# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d

# 等待服务启动
sleep 10

# 检查状态
docker-compose ps
```

### 步骤 4：测试访问

```bash
# 测试本地访问
curl -I http://localhost:3000/
curl -I http://localhost:8000/

# 测试通过域名访问
curl -I https://comp.uedmagazine.net/
```

## 🔧 常见配置错误

### 错误 1：location 顺序问题
```nginx
# ❌ 错误：location / 在前面会拦截所有请求
location / {
    proxy_pass http://127.0.0.1:3000;
}

location /api/ {
    proxy_pass http://127.0.0.1:8000;  # 永远不会被匹配
}
```

```nginx
# ✅ 正确：具体路径在前，通用路径在后
location /api/ {
    proxy_pass http://127.0.0.1:8000;
}

location / {
    proxy_pass http://127.0.0.1:3000;
}
```

### 错误 2：proxy_pass 末尾斜杠问题
```nginx
# ❌ 可能有问题
location /api/ {
    proxy_pass http://127.0.0.1:8000/api/;  # 双重路径
}

# ✅ 推荐
location /api/ {
    proxy_pass http://127.0.0.1:8000;  # 保持原路径
}
```

### 错误 3：缺少必要的头信息
```nginx
# ✅ 完整的代理头配置
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
```

## 📊 调试技巧

### 1. 启用详细日志
```nginx
# 在 server 块中添加
error_log /var/log/nginx/hypcup_error.log debug;
access_log /var/log/nginx/hypcup_access.log combined;
```

### 2. 添加调试头
```nginx
location / {
    proxy_pass http://127.0.0.1:3000;
    # 添加调试头
    add_header X-Debug-Location "frontend";
    add_header X-Debug-Upstream "127.0.0.1:3000";
}

location /api/ {
    proxy_pass http://127.0.0.1:8000;
    add_header X-Debug-Location "backend";
    add_header X-Debug-Upstream "127.0.0.1:8000";
}
```

### 3. 使用 curl 测试
```bash
# 测试并显示响应头
curl -I https://comp.uedmagazine.net/

# 测试并显示详细信息
curl -v https://comp.uedmagazine.net/

# 测试特定路径
curl -I https://comp.uedmagazine.net/api/works
curl -I https://comp.uedmagazine.net/work/63
```

## 🎯 快速修复清单

- [ ] 检查 Nginx 配置语法：`nginx -t`
- [ ] 确认 location 规则顺序正确
- [ ] 验证前端服务运行：`curl http://localhost:3000/`
- [ ] 验证后端服务运行：`curl http://localhost:8000/`
- [ ] 检查环境变量：`NEXT_PUBLIC_API_URL=/api`
- [ ] 重新加载 Nginx：`nginx -s reload`
- [ ] 重启容器：`docker-compose restart`
- [ ] 查看日志：`tail -f /var/log/nginx/error.log`

## 📞 如果问题仍然存在

1. 提供 Nginx 错误日志
2. 提供容器日志：`docker-compose logs`
3. 提供当前 Nginx 配置文件
4. 提供 `curl -v https://comp.uedmagazine.net/` 的完整输出
