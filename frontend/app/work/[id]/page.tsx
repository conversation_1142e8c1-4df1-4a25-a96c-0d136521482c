import { Metadata } from "next"
import { notFound } from "next/navigation"
import { WorkPageClient } from "./work-page-client"
import { getBackendApiUrl } from "@/lib/api-config"

// 服务端数据获取函数
async function getWork(id: string) {
  try {
    const apiUrl = getBackendApiUrl()
    const response = await fetch(`${apiUrl}/works/${id}`, {
      // 添加缓存控制
      next: { revalidate: 60 }, // 60秒缓存
    })

    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      throw new Error(`HTTP ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching work on server:", error)
    return null
  }
}

// 生成页面元数据
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const { id } = await params
  const work = await getWork(id)

  if (!work) {
    return {
      title: "作品未找到 - UIA 霍普杯国际大学生建筑设计竞赛",
      description: "抱歉，您访问的作品不存在。"
    }
  }

  return {
    title: `${work.title} - UIA 霍普杯国际大学生建筑设计竞赛`,
    description: work.description?.substring(0, 160) || "探索优秀的建筑设计竞赛作品，发现创新的建筑理念与设计思路",
    openGraph: {
      title: work.title,
      description: work.description?.substring(0, 160),
      images: work.cover_image ? [work.cover_image] : [],
    },
  }
}

// 服务端组件
export default async function WorkPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const work = await getWork(id)

  // 如果作品不存在，显示 404
  if (!work) {
    notFound()
  }

  // 将服务端获取的数据传递给客户端组件
  return <WorkPageClient initialWork={work} workId={id} />
}



