"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ChevronLeft, ChevronRight, Eye, Award, MapPin, Calendar, User, LogOut, Settings, Heart, Bookmark, LogIn } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar } from "@/components/avatar"
import { CommentSection } from "@/components/comment-section"
import { ImagePreview } from "@/components/image-preview"
import { useAuth } from "@/lib/auth"
import { AuthDialog } from "@/components/auth-dialog"
import { apiClient } from "@/lib/api"
import { notFound } from "next/navigation"
import { cn } from "@/lib/utils"
import { useScrollPosition, generateSc<PERSON><PERSON><PERSON> } from "@/lib/scroll-position"
import { useIsMobile } from "@/hooks/use-mobile"
import type { Work } from "@/lib/types"

export default function WorkPage({ params }: { params: Promise<{ id: string }> }) {
  const { user, logout } = useAuth()
  const router = useRouter()
  const isMobile = useIsMobile()
  const [work, setWork] = useState<Work | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [workId, setWorkId] = useState<string | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  const [likesCount, setLikesCount] = useState(0)

  // 滚动位置管理
  const { saveCurrentPosition, restorePosition } = useScrollPosition('home-page')

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setWorkId(resolvedParams.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    if (workId) {
      fetchWork()
    }
  }, [workId])

  const fetchWork = async () => {
    if (!workId) return
    try {
      setLoading(true)
      const workData = await apiClient.getWork(workId)
      setWork(workData)
      setIsLiked(workData.is_liked_by_user || false)
      setIsFavorited(workData.is_favorite_by_user || false)
      setLikesCount(workData.likes_count || 0)
    } catch (error) {
      console.error("Error fetching work:", error)
      notFound()
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <main className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="aspect-[16/10] bg-gray-200 rounded mb-6"></div>
            <div className="h-6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </main>
    )
  }

  if (!work) {
    notFound()
  }



  const handleLike = async () => {
    if (!user || !workId) return

    try {
      await apiClient.toggleLike(workId)
      setIsLiked(!isLiked)
      setLikesCount(prev => isLiked ? prev - 1 : prev + 1)
    } catch (error) {
      console.error("Error toggling like:", error)
    }
  }

  const handleFavorite = async () => {
    if (!user || !workId) return

    try {
      await apiClient.toggleFavorite(workId)
      setIsFavorited(!isFavorited)
    } catch (error) {
      console.error("Error toggling favorite:", error)
    }
  }

  const getAwardColor = (award: string) => {
    switch (award) {
      case "一等奖":
        return "bg-red-500"
      case "二等奖":
        return "bg-orange-500"
      case "三等奖":
        return "bg-amber-500"
      case "优秀奖":
        return "bg-blue-500"
      case "入围奖":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  // 处理后退按钮
  const handleBack = () => {
    // 尝试恢复首页滚动位置
    const restored = restorePosition(false)
    if (restored) {
      router.push('/')
      // 延迟恢复滚动位置，确保页面已加载
      setTimeout(() => {
        restorePosition(true)
      }, 100)
    } else {
      router.push('/')
    }
  }

  return (
    <main className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-background border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-14">
            <Button variant="ghost" size="icon" onClick={handleBack}>
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <div className="flex items-center gap-2">
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2">
                      <Avatar
                        src={user?.avatar_url}
                        alt={user?.username || user?.email}
                        size="sm"
                      />
                      <span className="hidden sm:inline">
                        {user?.username || user?.email}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href="/profile">
                        <User className="mr-2 h-4 w-4" />
                        个人中心
                      </Link>
                    </DropdownMenuItem>
                    {user?.is_admin && (
                      <DropdownMenuItem asChild>
                        <Link href="/admin">
                          <Settings className="mr-2 h-4 w-4" />
                          管理后台
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={logout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      退出登录
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <AuthDialog>
                  <Button variant="ghost" size="icon">
                    <User className="h-5 w-5" />
                  </Button>
                </AuthDialog>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Column - Image Gallery */}
          <div className="lg:col-span-3">
            <div className="relative aspect-[3/2] w-full cursor-pointer rounded-lg overflow-hidden shadow-lg" onClick={() => setIsPreviewOpen(true)}>
              <Image
                src={work.images && work.images.length > 0 ? work.images[currentImageIndex] : work.cover_image}
                alt={work.title}
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
                priority
              />

              {/* Image Navigation */}
              {work.images && work.images.length > 1 && (
                <>
                  {/* Previous/Next Buttons */}
                  <button
                    className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white w-6 h-6 sm:w-12 sm:h-12 rounded-full transition-colors z-40 flex items-center justify-center touch-target"
                    onClick={(e) => {
                      e.stopPropagation()
                      setCurrentImageIndex((prev) => (prev === 0 ? work.images.length - 1 : prev - 1))
                    }}
                  >
                    <ChevronLeft className="h-5 w-5 sm:h-6 sm:w-6" />
                  </button>
                  <button
                    className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white w-6 h-6 sm:w-12 sm:h-12 rounded-full transition-colors z-40 flex items-center justify-center touch-target"
                    onClick={(e) => {
                      e.stopPropagation()
                      setCurrentImageIndex((prev) => (prev === work.images.length - 1 ? 0 : prev + 1))
                    }}
                  >
                    <ChevronRight className="h-5 w-5 sm:h-6 sm:w-6" />
                  </button>

                  {/* Dots Navigation */}
                  <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-40">
                    {work.images.map((_, index) => (
                      <button
                        key={index}
                        data-nav-dot
                        className={cn(
                          "w-3 h-3 rounded-full transition-colors flex-shrink-0 force-circle",
                          "border-0 outline-none focus:outline-none",
                          "min-w-[12px] min-h-[12px]",
                          index === currentImageIndex ? "bg-white" : "bg-white/50",
                        )}
                        style={{
                          width: '12px',
                          height: '12px',
                          borderRadius: '50%'
                        }}
                        onClick={(e) => {
                          e.stopPropagation()
                          setCurrentImageIndex(index)
                        }}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Right Column - Work Info */}
          <div className="lg:col-span-1">
            {/* Award Badge */}
            <div className="mb-4">
              <Badge
                data-award-badge
                className={cn(
                  "text-white font-medium border-0",
                  getAwardColor(work.award),
                  isMobile ? "text-xs px-2 py-1 h-7" : "text-sm px-3 py-1.5 h-9",
                  "flex items-center justify-center whitespace-nowrap inline-flex"
                )}
                style={isMobile ? { minWidth: 'auto', width: 'auto' } : {}}
              >
                <Award className={cn("mr-1.5 flex-shrink-0", isMobile ? "h-3 w-3" : "h-4 w-4")} />
                <span className="whitespace-nowrap">{work.award}</span>
              </Badge>
            </div>

            {/* Title and Basic Info */}
            <div className="mb-4">
              <h1 className="text-xl font-bold mb-2 leading-tight">{work.title}</h1>
              <p className="text-sm text-muted-foreground mb-3">{work.architect}</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="flex items-center justify-center gap-1 text-blue-500 mb-1">
                  <Eye className="h-3 w-3" />
                </div>
                <div className="text-sm font-semibold">{work.views_count}</div>
                <div className="text-xs text-muted-foreground">浏览量</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="flex items-center justify-center gap-1 text-red-500 mb-1">
                  <Heart className="h-3 w-3" />
                </div>
                <div className="text-sm font-semibold">{likesCount}</div>
                <div className="text-xs text-muted-foreground">点赞量</div>
              </div>
            </div>

            {/* Work Details */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <div className="text-xs text-muted-foreground">竞赛年份</div>
                  <div className="text-sm font-medium">{work.year}年</div>
                </div>
              </div>
              {work.location && (
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-xs text-muted-foreground">学校/机构</div>
                    <div className="text-sm font-medium">{work.location}</div>
                  </div>
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="mb-4">
              <h3 className="text-xs font-medium text-muted-foreground mb-2">标签</h3>
              <div className="flex flex-wrap gap-1.5 mb-3">
                {work.tags.map((tag) => (
                  <Badge
                    key={tag}
                    data-tag-badge
                    variant="secondary"
                    className={cn(
                      "border-0 bg-gray-100 text-gray-700 font-normal",
                      isMobile ? "text-xs px-2 py-0.5 h-6" : "text-xs px-3 py-1 h-7",
                      "flex items-center justify-center whitespace-nowrap inline-flex"
                    )}
                    style={isMobile ? { minWidth: 'auto', width: 'auto' } : {}}
                  >
                    <span className="whitespace-nowrap">{tag}</span>
                  </Badge>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {user ? (
                  <>
                    <Button
                      variant={isLiked ? "default" : "outline"}
                      size="sm"
                      onClick={handleLike}
                      className={cn(
                        "flex-1 transition-colors",
                        isLiked ? "bg-red-500 hover:bg-red-600 text-white" : "hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                      )}
                    >
                      <Heart className={cn("h-4 w-4 mr-1", isLiked ? "fill-current" : "")} />
                      点赞
                    </Button>
                    <Button
                      variant={isFavorited ? "default" : "outline"}
                      size="sm"
                      onClick={handleFavorite}
                      className={cn(
                        "flex-1 transition-colors",
                        isFavorited ? "bg-blue-500 hover:bg-blue-600 text-white" : "hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                      )}
                    >
                      <Bookmark className={cn("h-4 w-4 mr-1", isFavorited ? "fill-current" : "")} />
                      收藏
                    </Button>
                  </>
                ) : (
                  <>
                    <AuthDialog>
                      <Button variant="outline" size="sm" className="flex-1 hover:bg-red-50 hover:text-red-600 hover:border-red-200">
                        <Heart className="h-4 w-4 mr-1" />
                        点赞
                      </Button>
                    </AuthDialog>
                    <AuthDialog>
                      <Button variant="outline" size="sm" className="flex-1 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200">
                        <Bookmark className="h-4 w-4 mr-1" />
                        收藏
                      </Button>
                    </AuthDialog>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Description Section */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">作品简介</h2>
          <div className="bg-gray-50 rounded-lg p-6">
            <p className="text-muted-foreground leading-relaxed whitespace-pre-line">{work.description}</p>
          </div>


        </div>

        {/* Comments Section */}
        <div className="mt-8">
          {workId && <CommentSection workId={workId} />}
        </div>
      </div>

      {/* Image Preview Modal */}
      {work.images && work.images.length > 0 && (
        <ImagePreview
          images={work.images}
          currentIndex={currentImageIndex}
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          onIndexChange={setCurrentImageIndex}
        />
      )}
    </main>
  )
}
