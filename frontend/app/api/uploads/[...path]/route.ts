import { type NextRequest, NextResponse } from "next/server"
import { getUploadsBaseUrl } from "@/lib/api-config"

// 获取上传文件的基础URL（不包含/api路径）
const UPLOADS_BASE_URL = "http://localhost:8000"

export async function GET(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  try {
    const { path } = await params
    const filePath = path.join('/')
    
    console.log("Frontend API - Upload file request:", filePath)

    // 构建后端文件URL
    const fileUrl = `${UPLOADS_BASE_URL}/uploads/${filePath}`
    
    console.log("Frontend API - Proxying to:", fileUrl)

    const response = await fetch(fileUrl)

    if (!response.ok) {
      console.log("Frontend API - File not found:", response.status)
      return NextResponse.json({ error: "File not found" }, { status: 404 })
    }

    // 获取文件内容和类型
    const fileBuffer = await response.arrayBuffer()
    const contentType = response.headers.get('content-type') || 'application/octet-stream'

    console.log("Frontend API - File found, content-type:", contentType)

    // 返回文件内容
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000', // 缓存1年
      },
    })
  } catch (error) {
    console.error("Frontend API - Upload file error:", error)
    return NextResponse.json({ error: "File access failed" }, { status: 500 })
  }
}
