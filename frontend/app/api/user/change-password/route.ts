import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const authHeader = request.headers.get("authorization")

    if (!authHeader) {
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    console.log("=== Frontend Change Password API ===")
    console.log("Received body:", body)

    // 转换字段名以匹配后端API期望的格式
    // 支持两种格式：camelCase 和 snake_case
    const backendBody = {
      current_password: body.currentPassword || body.current_password,
      new_password: body.newPassword || body.new_password
    }

    console.log("Backend body:", { ...backendBody, current_password: '***', new_password: '***' })

    const response = await fetch(`${API_BASE_URL}/user/change-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": authHeader,
      },
      body: JSON.stringify(backendBody),
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error changing password:", error)
    return NextResponse.json({ error: "Failed to change password" }, { status: 500 })
  }
}
