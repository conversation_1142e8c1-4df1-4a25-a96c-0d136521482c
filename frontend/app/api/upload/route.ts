import { NextRequest, NextResponse } from "next/server"
import { writeFile } from "fs/promises"
import { join } from "path"

export async function POST(request: NextRequest) {
  try {
    const data = await request.formData()
    const file: File | null = data.get("file") as unknown as File

    if (!file) {
      return NextResponse.json({ error: "No file uploaded" }, { status: 400 })
    }

    // 验证文件类型
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: "Invalid file type" }, { status: 400 })
    }

    // 验证文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: "File too large" }, { status: 400 })
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 生成唯一文件名
    const fileExtension = file.name.split(".").pop()
    const timestamp = Date.now()
    const fileName = `${timestamp}.${fileExtension}`
    const filePath = join(process.cwd(), "public", "uploads", "works", fileName)

    // 保存文件
    await writeFile(filePath, buffer)

    // 返回文件URL
    const fileUrl = `/uploads/works/${fileName}`
    
    return NextResponse.json({ 
      success: true, 
      url: fileUrl,
      filename: fileName 
    })
  } catch (error) {
    console.error("Error uploading file:", error)
    return NextResponse.json({ error: "Upload failed" }, { status: 500 })
  }
}
