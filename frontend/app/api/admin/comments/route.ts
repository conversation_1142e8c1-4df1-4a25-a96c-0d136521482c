import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const approved = searchParams.get("approved")
    const authHeader = request.headers.get("authorization")

    console.log("Frontend API - Auth header:", authHeader ? "Present" : "Missing")
    console.log("Frontend API - Approved param:", approved)

    if (!authHeader) {
      console.log("Frontend API - No auth header found")
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    const params = new URLSearchParams()
    if (approved !== null) {
      params.append("approved", approved)
    }

    const queryString = params.toString()
    const url = `${API_BASE_URL}/admin/comments${queryString ? `?${queryString}` : ""}`

    console.log("Frontend API - Calling backend URL:", url)

    const response = await fetch(url, {
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
      },
    })

    console.log("Frontend API - Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Frontend API - Backend error:", errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    console.log("Frontend API - Success, returning data")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Error:", error)
    return NextResponse.json({ error: "Failed to fetch comments" }, { status: 500 })
  }
}
