import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const authHeader = request.headers.get("authorization")

    console.log("Frontend API - Admin works GET request")
    console.log("Auth header:", authHeader ? "Present" : "Missing")

    if (!authHeader) {
      console.log("Frontend API - No auth header found")
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    // 构建查询参数
    const params = new URLSearchParams()
    for (const [key, value] of searchParams.entries()) {
      params.append(key, value)
    }

    const queryString = params.toString()
    const url = `${API_BASE_URL}/admin/works${queryString ? `?${queryString}` : ""}`

    console.log("Frontend API - Calling backend URL:", url)

    const response = await fetch(url, {
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
      },
    })

    console.log("Frontend API - Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Frontend API - Backend error:", errorText)
      
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { detail: errorText }
      }
      
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    console.log("Frontend API - Success, returning data")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Error:", error)
    return NextResponse.json({ error: "Failed to fetch admin works" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const authHeader = request.headers.get("authorization")

    console.log("Frontend API - Admin works POST request")

    if (!authHeader) {
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    const response = await fetch(`${API_BASE_URL}/admin/works`, {
      method: "POST",
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    console.log("Frontend API - Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { detail: errorText }
      }
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Error:", error)
    return NextResponse.json({ error: "Failed to create work" }, { status: 500 })
  }
}
