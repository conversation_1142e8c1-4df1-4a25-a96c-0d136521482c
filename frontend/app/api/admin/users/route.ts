import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")

    if (!authHeader) {
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()
    const backendUrl = `${API_BASE_URL}/admin/users${queryString ? `?${queryString}` : ""}`

    console.log("=== Frontend Admin Users API ===")
    console.log("Backend URL:", backendUrl)
    console.log("Authorization:", authHeader ? `${authHeader.substring(0, 20)}...` : "None")

    const response = await fetch(backendUrl, {
      method: "GET",
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    })

    console.log("Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Backend error:", errorText)
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { error: errorText }
      }
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    console.log("Admin users retrieved successfully")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Admin users error:", error)
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")

    if (!authHeader) {
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    const body = await request.json()
    const backendUrl = `${API_BASE_URL}/admin/users`

    console.log("=== Frontend Admin Create User API ===")
    console.log("Backend URL:", backendUrl)
    console.log("User data:", { ...body, password: body.password ? '***' : 'missing' })

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log("Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Backend error:", errorText)
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { error: errorText }
      }
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    console.log("User created successfully")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Create user error:", error)
    return NextResponse.json({ error: "Failed to create user" }, { status: 500 })
  }
}
