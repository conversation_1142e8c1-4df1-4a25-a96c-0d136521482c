import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function POST(request: NextRequest) {
  try {
    // 检查Content-Type来决定如何解析数据
    const contentType = request.headers.get('content-type') || ''
    let registerData: any

    if (contentType.includes('application/json')) {
      // JSON数据
      registerData = await request.json()
      console.log("=== Frontend Register API (JSON) ===")
      console.log("JSON data:", registerData)
    } else {
      // FormData数据
      const formData = await request.formData()
      console.log("=== Frontend Register API (FormData) ===")
      console.log("Form data:", Object.fromEntries(formData.entries()))

      registerData = {
        email: formData.get('email') as string,
        username: formData.get('username') as string,
        password: formData.get('password') as string,
      }
    }

    const backendUrl = `${API_BASE_URL}/auth/register`
    console.log("API_BASE_URL:", API_BASE_URL)
    console.log("Backend URL:", backendUrl)
    console.log("Register data:", registerData)

    // 验证必需字段
    if (!registerData.email || !registerData.username || !registerData.password) {
      console.log("Missing required fields:", registerData)
      return NextResponse.json({ detail: "Missing required fields" }, { status: 400 })
    }

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registerData),
    })

    console.log("Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Backend error:", errorText)
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { detail: errorText }
      }
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    console.log("Register successful, returning data")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Register error:", error)
    return NextResponse.json({ detail: "Registration failed" }, { status: 500 })
  }
}
