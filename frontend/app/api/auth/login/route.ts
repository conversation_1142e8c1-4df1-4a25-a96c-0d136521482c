import { type NextRequest, NextResponse } from "next/server"
import { getBackendApiUrl } from "@/lib/api-config"

// 前端 API 路由始终使用后端内部地址
const API_BASE_URL = getBackendApiUrl()

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const backendUrl = `${API_BASE_URL}/auth/login`

    console.log("=== Frontend Login API ===")
    console.log("API_BASE_URL:", API_BASE_URL)
    console.log("Backend URL:", backendUrl)
    console.log("Form data:", Object.fromEntries(formData.entries()))

    // Convert FormData to URLSearchParams for OAuth2PasswordRequestForm
    // 支持前端发送 'username' 或 'email' 字段
    const username = formData.get('username') as string || formData.get('email') as string
    const password = formData.get('password') as string

    console.log("Login attempt:", { username, password: password ? '***' : 'missing' })

    if (!username || !password) {
      return NextResponse.json({ detail: "Username and password are required" }, { status: 400 })
    }

    const urlParams = new URLSearchParams()
    urlParams.append('username', username)
    urlParams.append('password', password)

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: urlParams,
    })

    console.log("Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Backend error:", errorText)
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { detail: errorText }
      }
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    console.log("Login successful, returning data")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Login error:", error)
    return NextResponse.json({ detail: "Login failed" }, { status: 500 })
  }
}
