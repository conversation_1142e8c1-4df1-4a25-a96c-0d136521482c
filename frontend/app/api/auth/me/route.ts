import { type NextRequest, NextResponse } from "next/server"

// 动态获取后端API地址
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }
  
  // 容器内部通信或内网环境
  if (process.env.NODE_ENV === "production") {
    return "http://localhost:8000/api"
  }
  
  // 开发环境
  return "http://localhost:8000/api"
}

const API_BASE_URL = getApiBaseUrl()

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")

    console.log("Frontend API - Auth header:", authHeader ? "Present" : "Missing")

    if (!authHeader) {
      console.log("Frontend API - No auth header found")
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    const url = `${API_BASE_URL}/auth/me`
    console.log("Frontend API - Calling backend URL:", url)

    const response = await fetch(url, {
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
      },
    })

    console.log("Frontend API - Backend response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Frontend API - Backend error:", errorText)
      
      if (response.status === 401) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
      }
      
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    console.log("Frontend API - Success, returning user data")
    return NextResponse.json(data)
  } catch (error) {
    console.error("Frontend API - Error:", error)
    return NextResponse.json({ error: "Failed to get user info" }, { status: 500 })
  }
}