import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const authHeader = request.headers.get("authorization")

    if (!authHeader) {
      return NextResponse.json({ error: "Authorization required" }, { status: 401 })
    }

    // 不需要读取body，因为收藏操作不需要额外数据
    const response = await fetch(`${API_BASE_URL}/works/${id}/favorite`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": authHeader,
      },
    })

    console.log("Backend response status:", response.status)
    console.log("Backend response headers:", Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.log("Backend error response:", errorText)
      let error
      try {
        error = JSON.parse(errorText)
      } catch {
        error = { detail: errorText || "Failed to toggle favorite" }
      }
      return NextResponse.json({ error: error.detail }, { status: response.status })
    }

    // 处理可能的空响应
    const responseText = await response.text()
    console.log("Backend success response:", responseText)

    let data
    try {
      data = responseText ? JSON.parse(responseText) : { success: true }
    } catch (e) {
      console.log("Failed to parse response as JSON, treating as success")
      data = { success: true }
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error toggling favorite:", error)
    return NextResponse.json({ error: "Failed to toggle favorite" }, { status: 500 })
  }
}
