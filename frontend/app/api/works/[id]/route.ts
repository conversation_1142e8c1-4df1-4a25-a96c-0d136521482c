import { type NextRequest, NextResponse } from "next/server"

// 动态获取后端API地址
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }
  
  // 生产环境：使用localhost:8000（容器内部通信）
  if (process.env.NODE_ENV === "production") {
    return "http://localhost:8000/api"
  }
  
  // 开发环境
  return "http://localhost:8000/api"
}

const API_BASE_URL = getApiBaseUrl()

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const url = `${API_BASE_URL}/works/${id}`

    console.log(`代理请求: GET ${url}`)

    // 传递认证头
    const headers: HeadersInit = {}
    const authHeader = request.headers.get('authorization')
    if (authHeader) {
      headers.Authorization = authHeader
    }

    const response = await fetch(url, { headers })

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({ error: "Work not found" }, { status: 404 })
      }
      throw new Error(`HTTP ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching work:", error)
    return NextResponse.json({ error: "Work not found" }, { status: 404 })
  }
}
