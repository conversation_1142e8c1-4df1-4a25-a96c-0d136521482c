import { type NextRequest, NextResponse } from "next/server"

// 在服务端使用内部地址，在客户端使用公网地址
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? "http://localhost:8000/api"  // 容器内部通信
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const year = searchParams.get("year")
  const award = searchParams.get("award")
  const search = searchParams.get("search")

  try {
    // Build query parameters for backend API
    const params = new URLSearchParams()

    if (year && year !== "全部") {
      params.append("year", year)
    }

    if (award && award !== "全部") {
      params.append("award", award)
    }

    if (search) {
      params.append("search", search)
    }

    const queryString = params.toString()
    const url = `${API_BASE_URL}/works/${queryString ? `?${queryString}` : ""}`

    // 传递认证头
    const headers: HeadersInit = {}
    const authHeader = request.headers.get('authorization')
    if (authHeader) {
      headers.Authorization = authHeader
    }

    const response = await fetch(url, { headers })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching works:", error)
    return NextResponse.json({ error: "Failed to fetch works" }, { status: 500 })
  }
}
