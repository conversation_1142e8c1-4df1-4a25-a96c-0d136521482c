import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  console.log("=== Test API route called ===")
  return NextResponse.json({ message: "Test API working", timestamp: new Date().toISOString() })
}

export async function POST(request: NextRequest) {
  console.log("=== Test POST API route called ===")
  
  try {
    const body = await request.json()
    console.log("Request body:", body)
    
    return NextResponse.json({ 
      message: "Test POST API working", 
      received: body,
      timestamp: new Date().toISOString() 
    })
  } catch (error) {
    console.error("Test API error:", error)
    return NextResponse.json({ error: "Test API failed" }, { status: 500 })
  }
}
