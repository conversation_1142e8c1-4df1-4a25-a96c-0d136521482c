"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { WorkCard } from "@/components/work-card"
import { useAuth } from "@/lib/auth"
import { useIsMobile } from "@/hooks/use-mobile"
import { useScrollPosition } from "@/lib/scroll-position"
import type { Work } from "@/lib/types"

interface HomePageClientProps {
  initialWorks: Work[]
}

export function HomePageClient({ initialWorks }: HomePageClientProps) {
  const { user } = useAuth()
  const isMobile = useIsMobile()
  
  // 使用服务端传入的初始数据
  const [works, setWorks] = useState<Work[]>(initialWorks)
  const [loading, setLoading] = useState(false) // 不再需要初始加载状态

  const [selectedYear, setSelectedYear] = useState("全部")
  const [selectedAward, setSelectedAward] = useState("全部")
  const [searchQuery, setSearchQuery] = useState("")

  // 滚动位置管理
  const { saveCurrentPosition, restorePosition } = useScrollPosition('home-page')

  const fetchWorks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        year: selectedYear,
        award: selectedAward,
        search: searchQuery,
        ...(user && { userId: user.id }),
      })

      const response = await fetch(`/api/works?${params}`)
      if (response.ok) {
        const data = await response.json()
        setWorks(data)
      }
    } catch (error) {
      console.error("Error fetching works:", error)
    } finally {
      setLoading(false)
    }
  }

  // 只在筛选条件变化时重新获取数据
  useEffect(() => {
    // 如果是初始状态（全部筛选条件），使用服务端数据
    if (selectedYear === "全部" && selectedAward === "全部" && searchQuery === "" && !user) {
      return
    }
    
    // 否则重新获取数据
    fetchWorks()
  }, [selectedYear, selectedAward, searchQuery, user])

  // 页面加载完成后恢复滚动位置
  useEffect(() => {
    const timer = setTimeout(() => {
      restorePosition(true)
    }, 100)
    return () => clearTimeout(timer)
  }, [restorePosition])

  const handleWorkClick = (workId: string) => {
    saveCurrentPosition()
  }

  const years = ["全部", "2024", "2023", "2022", "2021"]
  const awards = ["全部", "一等奖", "二等奖", "三等奖", "优秀奖", "入围奖"]

  return (
    <div className="min-h-screen bg-background">
      <Header
        selectedYear={selectedYear}
        selectedAward={selectedAward}
        searchQuery={searchQuery}
        onYearChange={setSelectedYear}
        onAwardChange={setSelectedAward}
        onSearchChange={setSearchQuery}
        years={years}
        awards={awards}
      />

      <main className="container mx-auto px-4 py-6">
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="aspect-[3/4] bg-gray-200 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        ) : works.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {works.map((work) => (
              <WorkCard
                key={work.id}
                work={work}
                onClick={() => handleWorkClick(work.id.toString())}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暂无作品数据</p>
          </div>
        )}
      </main>
    </div>
  )
}
