"use client"

import { useEffect, useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminGuard } from "@/components/admin/admin-guard"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Users, Eye, Heart, MessageSquare, Clock } from "lucide-react"
import { apiClient } from "@/lib/api"

interface DashboardStats {
  total_works: number
  published_works: number
  draft_works: number
  total_users: number
  admin_users: number
  regular_users: number
  total_views: number
  total_likes: number
  total_comments: number
  pending_comments: number
  approved_comments: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    total_works: 0,
    published_works: 0,
    draft_works: 0,
    total_users: 0,
    admin_users: 0,
    regular_users: 0,
    total_views: 0,
    total_likes: 0,
    total_comments: 0,
    pending_comments: 0,
    approved_comments: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const data = await apiClient.getAdminStats()
      setStats(data)
    } catch (error) {
      console.error("Error fetching stats:", error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: "总作品数",
      value: stats.total_works,
      icon: FileText,
      color: "text-blue-600",
    },
    {
      title: "已发布作品",
      value: stats.published_works,
      icon: FileText,
      color: "text-green-600",
    },
    {
      title: "草稿作品",
      value: stats.draft_works,
      icon: FileText,
      color: "text-yellow-600",
    },
    {
      title: "用户总数",
      value: stats.total_users,
      icon: Users,
      color: "text-purple-600",
    },
    {
      title: "管理员数",
      value: stats.admin_users,
      icon: Users,
      color: "text-indigo-600",
    },
    {
      title: "总浏览量",
      value: stats.total_views,
      icon: Eye,
      color: "text-orange-600",
    },
    {
      title: "总点赞数",
      value: stats.total_likes,
      icon: Heart,
      color: "text-red-600",
    },
    {
      title: "总评论数",
      value: stats.total_comments,
      icon: MessageSquare,
      color: "text-cyan-600",
    },
    {
      title: "待审核评论",
      value: stats.pending_comments,
      icon: Clock,
      color: "text-amber-600",
    },
  ]

  return (
    <AdminGuard>
      <AdminLayout>
        <div className="p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
            <p className="text-gray-600">欢迎来到建筑作品管理后台</p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {statCards.map((stat) => {
              const Icon = stat.icon
              return (
                <Card key={stat.title}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                    <Icon className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{loading ? "..." : stat.value.toLocaleString()}</div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>最近发布的作品</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded"></div>
                      <div>
                        <p className="font-medium">作品标题 {i}</p>
                        <p className="text-sm text-gray-500">2小时前发布</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>最新用户</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                      <div>
                        <p className="font-medium">用户{i}</p>
                        <p className="text-sm text-gray-500">1天前注册</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </AdminLayout>
    </AdminGuard>
  )
}
