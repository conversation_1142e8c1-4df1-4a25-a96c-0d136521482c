"use client"

import { useEffect, useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminGuard } from "@/components/admin/admin-guard"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Check, X, Trash2, MessageSquare, Clock, CheckCircle, XCircle } from "lucide-react"
import { apiClient } from "@/lib/api"
import { useAuth } from "@/lib/auth"
import { useToast } from "@/components/toast"
import { Avatar } from "@/components/avatar"

interface Comment {
  id: number
  work_id: number
  user_id: number
  username: string
  user_avatar?: string
  work_title: string
  content: string
  created_at: string
  is_approved: boolean
  parent_id?: number
}

export default function AdminCommentsPage() {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [filterApproved, setFilterApproved] = useState<string>("all")
  const { user } = useAuth()
  const { showToast } = useToast()

  useEffect(() => {
    if (user?.is_admin) {
      fetchComments()
    }
  }, [user, filterApproved])

  const fetchComments = async () => {
    try {
      setLoading(true)
      const params: any = {}
      
      if (filterApproved !== "all") {
        params.approved = filterApproved === "approved"
      }

      const data = await apiClient.getAdminComments(params)
      setComments(data)
    } catch (error) {
      console.error("Error fetching comments:", error)
      showToast("获取评论列表失败", "error")
    } finally {
      setLoading(false)
    }
  }

  const handleApproveComment = async (commentId: number) => {
    try {
      await apiClient.approveComment(commentId.toString())
      showToast("评论已审核通过", "success")
      fetchComments()
    } catch (error) {
      console.error("Error approving comment:", error)
      showToast("审核失败", "error")
    }
  }

  const handleRejectComment = async (commentId: number) => {
    try {
      await apiClient.rejectComment(commentId.toString())
      showToast("评论已拒绝", "success")
      fetchComments()
    } catch (error) {
      console.error("Error rejecting comment:", error)
      showToast("拒绝失败", "error")
    }
  }

  const handleDeleteComment = async (commentId: number) => {
    if (!confirm("确定要删除这条评论吗？此操作不可撤销。")) {
      return
    }

    try {
      await apiClient.deleteAdminComment(commentId.toString())
      showToast("评论已删除", "success")
      fetchComments()
    } catch (error) {
      console.error("Error deleting comment:", error)
      showToast("删除失败", "error")
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + "..."
  }

  return (
    <AdminGuard>
      <AdminLayout>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">评论管理</h1>
              <p className="text-gray-600">管理所有用户评论</p>
            </div>
          </div>

          {/* 筛选 */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium">筛选状态:</span>
                <Select value={filterApproved} onValueChange={setFilterApproved}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="审核状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部评论</SelectItem>
                    <SelectItem value="approved">已审核</SelectItem>
                    <SelectItem value="pending">待审核</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <Card key={comment.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <Avatar
                          src={comment.user_avatar}
                          alt={comment.username}
                          size="sm"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-medium">{comment.username}</h3>
                            <Badge variant={comment.is_approved ? "default" : "secondary"}>
                              {comment.is_approved ? (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  已审核
                                </>
                              ) : (
                                <>
                                  <Clock className="h-3 w-3 mr-1" />
                                  待审核
                                </>
                              )}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            评论作品: <span className="font-medium">{comment.work_title}</span>
                          </p>
                          <p className="text-gray-800 mb-2 leading-relaxed">
                            {truncateContent(comment.content)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(comment.created_at)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        {!comment.is_approved && (
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => handleApproveComment(comment.id)}
                          >
                            <Check className="h-3 w-3 mr-1" />
                            通过
                          </Button>
                        )}
                        
                        {comment.is_approved && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleRejectComment(comment.id)}
                          >
                            <X className="h-3 w-3 mr-1" />
                            拒绝
                          </Button>
                        )}
                        
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteComment(comment.id)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          删除
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!loading && comments.length === 0 && (
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">没有找到评论</p>
            </div>
          )}
        </div>
      </AdminLayout>
    </AdminGuard>
  )
}
