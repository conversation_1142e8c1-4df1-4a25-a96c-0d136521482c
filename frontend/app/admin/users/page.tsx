"use client"

import { useEffect, useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminGuard } from "@/components/admin/admin-guard"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Shield, ShieldOff, User, UserX, Trash2, UserCheck, Search, Plus } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/lib/auth"
import { useToast } from "@/components/toast"
import { apiClient } from "@/lib/api"
import { Avatar } from "@/components/avatar"

interface UserData {
  id: number
  email: string
  username: string
  is_active: boolean
  is_admin: boolean
  avatar_url: string | null
  created_at: string
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<UserData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterAdmin, setFilterAdmin] = useState<string>("all")
  const [filterActive, setFilterActive] = useState<string>("all")
  const { user } = useAuth()
  const { showToast } = useToast()

  useEffect(() => {
    if (user?.is_admin) {
      fetchUsers()
    }
  }, [user, searchTerm, filterAdmin, filterActive])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const params: any = {}

      if (searchTerm) {
        params.search = searchTerm
      }

      if (filterAdmin !== "all") {
        params.is_admin = filterAdmin === "admin"
      }

      if (filterActive !== "all") {
        params.is_active = filterActive === "active"
      }

      const data = await apiClient.getAdminUsers(params)
      setUsers(data)
    } catch (error) {
      console.error("Error fetching users:", error)
      showToast("获取用户列表失败", "error")
    } finally {
      setLoading(false)
    }
  }

  const handleToggleAdmin = async (userId: number) => {
    try {
      await apiClient.toggleUserAdmin(userId.toString())
      showToast("管理员权限已更新", "success")
      fetchUsers()
    } catch (error) {
      console.error("Error toggling admin status:", error)
      showToast("操作失败", "error")
    }
  }

  const handleToggleActive = async (userId: number, currentStatus: boolean) => {
    try {
      await apiClient.toggleUserActive(userId.toString())
      showToast(currentStatus ? "用户已禁用" : "用户已启用", "success")
      fetchUsers()
    } catch (error) {
      console.error("Error toggling user active status:", error)
      showToast("操作失败", "error")
    }
  }

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销，将同时删除该用户的所有评论、点赞和收藏记录。`)) {
      return
    }

    try {
      await apiClient.deleteAdminUser(userId.toString())
      showToast("用户已删除", "success")
      fetchUsers()
    } catch (error) {
      console.error("Error deleting user:", error)
      showToast("删除失败", "error")
    }
  }

  return (
    <AdminGuard>
      <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
            <p className="text-gray-600">管理所有用户账户</p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            添加用户
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索用户名或邮箱..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterAdmin} onValueChange={setFilterAdmin}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="管理员状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部用户</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="user">普通用户</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterActive} onValueChange={setFilterActive}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="激活状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">已激活</SelectItem>
                  <SelectItem value="inactive">已禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {users.map((userData) => (
              <Card key={userData.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar
                        src={userData.avatar_url}
                        alt={userData.username}
                        size="md"
                      />
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{userData.username}</h3>
                          <div className="flex gap-1">
                            {userData.is_admin && (
                              <Badge variant="destructive">管理员</Badge>
                            )}
                            <Badge variant={userData.is_active ? "default" : "secondary"}>
                              {userData.is_active ? "活跃" : "禁用"}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">{userData.email}</p>
                        <p className="text-xs text-gray-500">
                          注册时间: {new Date(userData.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleAdmin(userData.id)}
                        disabled={userData.id === user?.id}
                      >
                        {userData.is_admin ? (
                          <>
                            <ShieldOff className="h-3 w-3 mr-1" />
                            取消管理员
                          </>
                        ) : (
                          <>
                            <Shield className="h-3 w-3 mr-1" />
                            设为管理员
                          </>
                        )}
                      </Button>

                      <Button
                        size="sm"
                        variant={userData.is_active ? "secondary" : "default"}
                        onClick={() => handleToggleActive(userData.id, userData.is_active)}
                        disabled={userData.id === user?.id}
                      >
                        {userData.is_active ? (
                          <>
                            <UserX className="h-3 w-3 mr-1" />
                            禁用
                          </>
                        ) : (
                          <>
                            <UserCheck className="h-3 w-3 mr-1" />
                            启用
                          </>
                        )}
                      </Button>

                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDeleteUser(userData.id, userData.username)}
                        disabled={userData.id === user?.id}
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        删除
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {!loading && users.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无用户</p>
          </div>
        )}
      </div>
    </AdminLayout>
    </AdminGuard>
  )
}
