"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminGuard } from "@/components/admin/admin-guard"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/lib/auth"

export default function AdminSettingsPage() {
  return (
    <AdminGuard>
      <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
          <p className="text-gray-600">管理系统配置和设置</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>网站信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">网站名称</label>
                  <p className="text-gray-900">建筑作品展示平台</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">版本</label>
                  <p className="text-gray-900">v1.0.0</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>存储设置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">图片存储方式</label>
                  <p className="text-gray-900">本地存储</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">存储路径</label>
                  <p className="text-gray-900">/public/uploads</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
    </AdminGuard>
  )
}
