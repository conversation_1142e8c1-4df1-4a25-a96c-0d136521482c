"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Edit, Eye, EyeOff, Star, Trash2 } from "lucide-react"
import { useAuth } from "@/lib/auth"
import { apiClient } from "@/lib/api"
import Link from "next/link"

interface Work {
  id: number
  title: string
  architect: string
  year: number
  award: string
  category: string
  description: string
  location: string
  cover_image: string
  images: string[]
  tags: string[]
  is_published: boolean
  is_featured: boolean
  likes_count: number
  views_count: number
  created_at: string
  updated_at?: string
  published_at?: string
}

export default function WorkDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [work, setWork] = useState<Work | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.is_admin && params.id) {
      fetchWork()
    }
  }, [user, params.id])

  const fetchWork = async () => {
    try {
      setLoading(true)
      const workData = await apiClient.getAdminWork(params.id as string)
      setWork(workData)
    } catch (error) {
      console.error("Error fetching work:", error)
    } finally {
      setLoading(false)
    }
  }

  const handlePublish = async () => {
    if (!work) return
    try {
      await apiClient.publishWork(work.id.toString())
      setWork({ ...work, is_published: true })
    } catch (error) {
      console.error("Error publishing work:", error)
    }
  }

  const handleUnpublish = async () => {
    if (!work) return
    try {
      await apiClient.unpublishWork(work.id.toString())
      setWork({ ...work, is_published: false })
    } catch (error) {
      console.error("Error unpublishing work:", error)
    }
  }

  const handleToggleFeatured = async () => {
    if (!work) return
    try {
      await apiClient.toggleFeatured(work.id.toString())
      setWork({ ...work, is_featured: !work.is_featured })
    } catch (error) {
      console.error("Error toggling featured:", error)
    }
  }

  const handleDelete = async () => {
    if (!work) return
    if (confirm("确定要删除这个作品吗？此操作不可撤销。")) {
      try {
        await apiClient.deleteWork(work.id.toString())
        router.push("/admin/works")
      } catch (error) {
        console.error("Error deleting work:", error)
      }
    }
  }

  if (!user?.is_admin) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
            <p className="text-gray-600">您没有权限访问此页面。</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!work) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">作品未找到</h1>
            <Button onClick={() => router.back()}>返回</Button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{work.title}</h1>
            <p className="text-gray-600">作品详情</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.back()}>
              返回列表
            </Button>
            <Button asChild>
              <Link href={`/admin/works/${work.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要信息 */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle>基本信息</CardTitle>
                  <div className="flex gap-2">
                    {work.is_featured && (
                      <Badge variant="secondary">
                        <Star className="h-3 w-3 mr-1" />
                        精选
                      </Badge>
                    )}
                    <Badge variant={work.is_published ? "default" : "outline"}>
                      {work.is_published ? "已发布" : "草稿"}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">建筑师</label>
                    <p className="text-gray-900">{work.architect}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">年份</label>
                    <p className="text-gray-900">{work.year}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">奖项</label>
                    <p className="text-gray-900">{work.award}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">分类</label>
                    <p className="text-gray-900">{work.category}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">位置</label>
                    <p className="text-gray-900">{work.location}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">统计</label>
                    <p className="text-gray-900">👁️ {work.views_count} ❤️ {work.likes_count}</p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-700">描述</label>
                  <p className="text-gray-900 mt-1">{work.description}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">标签</label>
                  <div className="flex gap-2 mt-1">
                    {work.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">{tag}</Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 图片展示 */}
            <Card>
              <CardHeader>
                <CardTitle>图片</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">封面图片</label>
                    <img
                      src={work.cover_image}
                      alt={work.title}
                      className="w-full h-64 object-cover rounded-lg mt-2"
                    />
                  </div>
                  
                  {work.images.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">其他图片</label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                        {work.images.map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`${work.title} - ${index + 1}`}
                            className="w-full h-32 object-cover rounded-lg"
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 操作面板 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={work.is_published ? handleUnpublish : handlePublish}
                >
                  {work.is_published ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      取消发布
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      发布作品
                    </>
                  )}
                </Button>

                <Button
                  className="w-full"
                  variant="outline"
                  onClick={handleToggleFeatured}
                >
                  <Star className="h-4 w-4 mr-2" />
                  {work.is_featured ? "取消精选" : "设为精选"}
                </Button>

                <Button
                  className="w-full"
                  variant="destructive"
                  onClick={handleDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除作品
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>时间信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div>
                  <label className="font-medium text-gray-700">创建时间</label>
                  <p className="text-gray-600">{new Date(work.created_at).toLocaleString()}</p>
                </div>
                {work.updated_at && (
                  <div>
                    <label className="font-medium text-gray-700">更新时间</label>
                    <p className="text-gray-600">{new Date(work.updated_at).toLocaleString()}</p>
                  </div>
                )}
                {work.published_at && (
                  <div>
                    <label className="font-medium text-gray-700">发布时间</label>
                    <p className="text-gray-600">{new Date(work.published_at).toLocaleString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
