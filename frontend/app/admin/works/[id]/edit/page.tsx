"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON>, usePara<PERSON> } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ImageUpload } from "@/components/ui/image-upload"
import { useAuth } from "@/lib/auth"
import { apiClient } from "@/lib/api"

interface WorkFormData {
  title: string
  architect: string
  year: number
  award: string

  description: string
  location: string
  cover_image: string
  images: string[]
  tags: string[]
  is_published: boolean
  is_featured: boolean
}

export default function EditWorkPage() {
  const router = useRouter()
  const params = useParams()
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [formData, setFormData] = useState<WorkFormData>({
    title: "",
    architect: "",
    year: new Date().getFullYear(),
    award: "",

    description: "",
    location: "",
    cover_image: "",
    images: [],
    tags: [],
    is_published: false,
    is_featured: false,
  })

  const categories = [
    { value: "residential", label: "住宅建筑" },
    { value: "commercial", label: "商业建筑" },
    { value: "educational", label: "教育建筑" },
    { value: "cultural", label: "文化建筑" },
    { value: "public", label: "公共建筑" },
    { value: "landscape", label: "景观设计" },
    { value: "urban", label: "城市规划" },
    { value: "interior", label: "室内设计" },
  ]

  const awards = [
    "一等奖",
    "二等奖",
    "三等奖",
    "优秀奖",
    "入围奖",
  ]

  useEffect(() => {
    if (user?.is_admin && params.id) {
      fetchWork()
    }
  }, [user, params.id])

  const fetchWork = async () => {
    try {
      setInitialLoading(true)
      const work = await apiClient.getAdminWork(params.id as string)
      setFormData({
        title: work.title || "",
        architect: work.architect || "",
        year: work.year || new Date().getFullYear(),
        award: work.award || "",

        description: work.description || "",
        location: work.location || "",
        cover_image: work.cover_image || "",
        images: work.images || [],
        tags: work.tags || [],
        is_published: work.is_published || false,
        is_featured: work.is_featured || false,
      })
    } catch (error) {
      console.error("Error fetching work:", error)
    } finally {
      setInitialLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user?.is_admin) return

    setLoading(true)
    try {
      await apiClient.updateWork(params.id as string, formData)
      router.push("/admin/works")
    } catch (error) {
      console.error("Error updating work:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleTagsChange = (value: string) => {
    const tags = value.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0)
    setFormData({ ...formData, tags })
  }

  if (!user?.is_admin) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
            <p className="text-gray-600">您没有权限访问此页面。</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (initialLoading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">编辑作品</h1>
          <p className="text-gray-600">修改建筑作品信息</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>作品信息</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="title">作品标题 *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="architect">建筑师 *</Label>
                  <Input
                    id="architect"
                    value={formData.architect}
                    onChange={(e) => setFormData({ ...formData, architect: e.target.value })}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="year">年份 *</Label>
                  <Input
                    id="year"
                    type="number"
                    value={formData.year}
                    onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                    required
                  />
                </div>



                <div>
                  <Label htmlFor="award">奖项</Label>
                  <Select value={formData.award} onValueChange={(value) => setFormData({ ...formData, award: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择奖项" />
                    </SelectTrigger>
                    <SelectContent>
                      {awards.map((award) => (
                        <SelectItem key={award} value={award}>
                          {award}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="location">位置</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="tags">标签 (用逗号分隔)</Label>
                <Input
                  id="tags"
                  value={formData.tags.join(", ")}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="现代主义, 可持续设计, 城市更新"
                />
              </div>

              <div>
                <Label>封面图片</Label>
                <ImageUpload
                  value={formData.cover_image ? [formData.cover_image] : []}
                  onChange={(images) => setFormData({ ...formData, cover_image: images[0] || "" })}
                  maxFiles={1}
                />
              </div>

              <div>
                <Label>作品图片</Label>
                <ImageUpload
                  value={formData.images}
                  onChange={(images) => setFormData({ ...formData, images })}
                  maxFiles={10}
                />
              </div>

              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.is_published}
                    onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
                  />
                  已发布
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.is_featured}
                    onChange={(e) => setFormData({ ...formData, is_featured: e.target.checked })}
                  />
                  精选作品
                </label>
              </div>

              <div className="flex gap-4">
                <Button type="submit" disabled={loading}>
                  {loading ? "保存中..." : "保存更改"}
                </Button>
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
