"use client"

import { useEffect, useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminGuard } from "@/components/admin/admin-guard"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Trash2, Eye, EyeOff } from "lucide-react"
import Link from "next/link"
import { apiClient } from "@/lib/api"
import { useAuth } from "@/lib/auth"

interface Work {
  id: number
  title: string
  architect: string
  year: number
  award: string
  category: string
  is_published: boolean
  is_featured: boolean
  likes_count: number
  views_count: number
  created_at: string
}

export default function AdminWorksPage() {
  const [works, setWorks] = useState<Work[]>([])
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  useEffect(() => {
    if (user?.is_admin) {
      fetchWorks()
    }
  }, [user])

  const fetchWorks = async () => {
    try {
      setLoading(true)
      const data = await apiClient.getAdminWorks()
      setWorks(data)
    } catch (error) {
      console.error("Error fetching works:", error)
    } finally {
      setLoading(false)
    }
  }

  const handlePublish = async (id: number) => {
    try {
      await apiClient.publishWork(id.toString())
      fetchWorks()
    } catch (error) {
      console.error("Error publishing work:", error)
    }
  }

  const handleUnpublish = async (id: number) => {
    try {
      await apiClient.unpublishWork(id.toString())
      fetchWorks()
    } catch (error) {
      console.error("Error unpublishing work:", error)
    }
  }

  const handleToggleFeatured = async (id: number) => {
    try {
      await apiClient.toggleFeatured(id.toString())
      fetchWorks()
    } catch (error) {
      console.error("Error toggling featured:", error)
    }
  }

  const handleDelete = async (id: number) => {
    if (confirm("确定要删除这个作品吗？")) {
      try {
        await apiClient.deleteWork(id.toString())
        fetchWorks()
      } catch (error) {
        console.error("Error deleting work:", error)
      }
    }
  }

  return (
    <AdminGuard>
      <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">作品管理</h1>
            <p className="text-gray-600">管理所有建筑作品</p>
          </div>
          <Button asChild>
            <Link href="/admin/works/add">
              <Plus className="h-4 w-4 mr-2" />
              添加作品
            </Link>
          </Button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {works.map((work) => (
              <Card key={work.id}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <Link href={`/admin/works/${work.id}`} className="hover:text-blue-600">
                      <CardTitle className="text-lg line-clamp-2">{work.title}</CardTitle>
                    </Link>
                    <div className="flex gap-1">
                      {work.is_featured && (
                        <Badge variant="secondary">精选</Badge>
                      )}
                      <Badge variant={work.is_published ? "default" : "outline"}>
                        {work.is_published ? "已发布" : "草稿"}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* 封面缩略图 */}
                  <div className="relative aspect-[4/3] overflow-hidden rounded-lg mb-4">
                    <img
                      src={work.cover_image || "/placeholder.svg"}
                      alt={work.title}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <p>建筑师: {work.architect}</p>
                    <p>年份: {work.year}</p>
                    <p>奖项: {work.award}</p>
                    <p>分类: {work.category}</p>
                    <div className="flex gap-4">
                      <span>👁️ {work.views_count}</span>
                      <span>❤️ {work.likes_count}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/admin/works/${work.id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        查看
                      </Link>
                    </Button>

                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/admin/works/${work.id}/edit`}>
                        <Edit className="h-3 w-3 mr-1" />
                        编辑
                      </Link>
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => work.is_published ? handleUnpublish(work.id) : handlePublish(work.id)}
                    >
                      {work.is_published ? (
                        <>
                          <EyeOff className="h-3 w-3 mr-1" />
                          取消发布
                        </>
                      ) : (
                        <>
                          <Eye className="h-3 w-3 mr-1" />
                          发布
                        </>
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleFeatured(work.id)}
                    >
                      {work.is_featured ? "取消精选" : "设为精选"}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(work.id)}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      删除
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {!loading && works.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无作品</p>
          </div>
        )}
      </div>
    </AdminLayout>
    </AdminGuard>
  )
}
