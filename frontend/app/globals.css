@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 移动端优化样式 */
@layer utilities {
  /* 触摸友好的按钮 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 移动端文本选择优化 */
  .mobile-text {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 移动端滚动优化 */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 移动端点击反馈 */
  .mobile-tap {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* 平滑过渡动效 */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .smooth-height {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .smooth-opacity {
    transition: opacity 0.3s ease-in-out;
  }

  /* 移动端Tabs优化 */
  .mobile-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .mobile-tabs::-webkit-scrollbar {
    display: none;
  }

  /* 移动端卡片优化 */
  .mobile-card {
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  /* 移动端表单优化 */
  .mobile-form-input {
    border-radius: 0.5rem;
    border-width: 1px;
    padding: 0.75rem 1rem;
  }
}

/* 移动端媒体查询 */
@media (max-width: 768px) {
  /* 确保容器在小屏幕上有合适的内边距 */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 优化表单元素在移动端的显示 */
  input, select, textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 优化按钮在移动端的显示 */
  button {
    min-height: 44px; /* 触摸友好的最小高度 */
  }

  /* 修复登录对话框选项卡样式 */
  [data-radix-tabs-list] {
    overflow: hidden;
  }

  [data-radix-tabs-trigger] {
    margin: 0 2px;
  }

  /* 确保图片导航按钮在移动端保持圆形 */
  .rounded-full {
    border-radius: 50% !important;
    aspect-ratio: 1 / 1;
  }

  /* 移动端Badge优化 */
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    overflow: hidden;
  }

  /* 确保Badge文本不会溢出 */
  .badge span {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
  }

  /* 移动端触摸优化 */
  .touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* 强制修复移动端组件形状 */

  /* 奖项和标签自适应宽度优化 */
  [data-award-badge], [data-tag-badge] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    white-space: nowrap !important;
    border-radius: 9999px !important;
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
  }

  /* 移动端奖项标签优化 */
  @media (max-width: 768px) {
    [data-award-badge] {
      height: 20px !important;
      padding: 2px 6px !important;
      font-size: 10px !important;
    }

    [data-tag-badge] {
      height: 16px !important;
      padding: 2px 6px !important;
      font-size: 10px !important;
    }
  }

  /* 桌面端样式 */
  @media (min-width: 769px) {
    [data-award-badge] {
      height: 28px !important;
      padding: 4px 10px !important;
      font-size: 12px !important;
    }

    [data-tag-badge] {
      height: 28px !important;
      padding: 4px 12px !important;
      font-size: 12px !important;
    }
  }

  /* 导航指示点统一样式 */
  [data-nav-dot] {
    width: 12px !important;
    height: 12px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
  }

  /* 移动端强制圆形 */
  .force-circle {
    border-radius: 50% !important;
    aspect-ratio: 1 / 1 !important;
  }

  /* 移动端强制修复所有Badge组件 */
  .badge, [class*="badge"] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    white-space: nowrap !important;
    border-radius: 9999px !important;
  }

  /* 移动端强制修复导航点 */
  button[data-nav-dot] {
    width: 12px !important;
    height: 12px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
    min-width: 12px !important;
    min-height: 12px !important;
  }
}
