import { Metadata } from "next"
import { HomePageClient } from "./home-page-client"
import { getBackendApiUrl } from "@/lib/api-config"

// 服务端数据获取函数
async function getWorks() {
  try {
    const apiUrl = getBackendApiUrl()
    const response = await fetch(`${apiUrl}/works/`, {
      // 添加缓存控制
      next: { revalidate: 30 }, // 30秒缓存
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching works on server:", error)
    return []
  }
}

// 页面元数据
export const metadata: Metadata = {
  title: "UIA 霍普杯国际大学生建筑设计竞赛-2024线上作品巡展",
  description: "探索优秀的建筑设计竞赛作品，发现创新的建筑理念与设计思路",
  openGraph: {
    title: "UIA 霍普杯国际大学生建筑设计竞赛-2024线上作品巡展",
    description: "探索优秀的建筑设计竞赛作品，发现创新的建筑理念与设计思路",
    type: "website",
  },
}

// 服务端组件
export default async function Home() {
  const initialWorks = await getWorks()

  // 将服务端获取的数据传递给客户端组件
  return <HomePageClient initialWorks={initialWorks} />
}
