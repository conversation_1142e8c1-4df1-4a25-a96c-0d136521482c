"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { WorkCard } from "@/components/work-card"
import { useAuth } from "@/lib/auth"
import { useIsMobile } from "@/hooks/use-mobile"
import { useScrollPosition } from "@/lib/scroll-position"
import type { Work } from "@/lib/types"

export default function Home() {
  const { user } = useAuth()
  const isMobile = useIsMobile()
  const [works, setWorks] = useState<Work[]>([])
  const [loading, setLoading] = useState(true)

  const [selectedYear, setSelectedYear] = useState("全部")
  const [selectedAward, setSelectedAward] = useState("全部")
  const [searchQuery, setSearchQuery] = useState("")

  // 滚动位置管理
  const { saveCurrentPosition, restorePosition } = useScrollPosition('home-page')

  const fetchWorks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        year: selectedYear,
        award: selectedAward,
        search: searchQuery,
        ...(user && { userId: user.id }),
      })

      const response = await fetch(`/api/works?${params}`)
      if (response.ok) {
        const data = await response.json()
        setWorks(data)
      }
    } catch (error) {
      console.error("Error fetching works:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchWorks()
  }, [selectedYear, selectedAward, searchQuery, user])

  // 页面加载完成后恢复滚动位置
  useEffect(() => {
    const timer = setTimeout(() => {
      restorePosition(false)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // 在页面卸载前保存滚动位置
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveCurrentPosition()
    }

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        saveCurrentPosition()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return (
    <main className="min-h-screen bg-background">
      <Header
        selectedYear={selectedYear}
        selectedAward={selectedAward}
        onYearChange={setSelectedYear}
        onAwardChange={setSelectedAward}
        onSearch={setSearchQuery}
      />

      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
        {searchQuery && (
          <div className="mb-4 sm:mb-6">
            <h2 className="text-base sm:text-lg font-semibold">
              搜索结果："{searchQuery}" ({works.length} 个作品)
            </h2>
          </div>
        )}

        {loading ? (
          <div className={`grid gap-4 sm:gap-6 ${
            isMobile
              ? "grid-cols-1"
              : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
          }`}>
            {Array.from({ length: isMobile ? 4 : 8 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 aspect-[4/3] rounded-lg mb-3 sm:mb-4"></div>
                <div className="bg-gray-200 h-4 rounded mb-2"></div>
                <div className="bg-gray-200 h-3 rounded mb-2"></div>
                <div className="bg-gray-200 h-3 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className={`grid gap-4 sm:gap-6 ${
            isMobile
              ? "grid-cols-1"
              : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
          }`}>
            {works.map((work) => (
              <WorkCard key={work.id} work={work} onUpdate={fetchWorks} />
            ))}
          </div>
        )}

        {!loading && works.length === 0 && (
          <div className="text-center py-8 sm:py-12">
            <p className="text-muted-foreground text-sm sm:text-base">没有找到符合条件的作品</p>
          </div>
        )}
      </div>
    </main>
  )
}
