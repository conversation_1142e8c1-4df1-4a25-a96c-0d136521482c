"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth"
import { WorkCard } from "@/components/work-card"
import { Avatar } from "@/components/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/toast"
import { apiClient } from "@/lib/api"
import { Heart, Bookmark, Settings, User, Lock, ArrowLeft, Edit } from "lucide-react"
import { redirect } from "next/navigation"
import Link from "next/link"
import type { Work } from "@/lib/types"

export default function ProfilePage() {
  const { user, logout } = useAuth()
  const { showToast } = useToast()
  const [likedWorks, setLikedWorks] = useState<Work[]>([])
  const [favoriteWorks, setFavoriteWorks] = useState<Work[]>([])
  const [loading, setLoading] = useState(true)
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  useEffect(() => {
    if (!user) {
      redirect("/")
    } else {
      fetchUserData()
    }
  }, [user])

  const fetchUserData = async () => {
    try {
      setLoading(true)

      // 获取用户点赞的作品
      const liked = await apiClient.getUserLikedWorks()
      setLikedWorks(liked)

      // 获取用户收藏的作品
      const favorites = await apiClient.getUserFavoriteWorks()
      setFavoriteWorks(favorites)
    } catch (error) {
      console.error("Error fetching user data:", error)
      showToast("获取用户数据失败", "error")
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      showToast("新密码和确认密码不匹配", "error")
      return
    }

    if (passwordForm.newPassword.length < 6) {
      showToast("密码长度至少6位", "error")
      return
    }

    try {
      await apiClient.changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
      })
      showToast("密码修改成功", "success")
      setPasswordForm({ currentPassword: "", newPassword: "", confirmPassword: "" })
    } catch (error) {
      console.error("Error changing password:", error)
      showToast("密码修改失败", "error")
    }
  }

  if (!user) {
    return null
  }

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-4xl">
        {/* Header with Back Button */}
        <div className="flex items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          <Link href="/">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 sm:gap-2 touch-target px-2 sm:px-3"
            >
              <ArrowLeft className="h-4 w-4 flex-shrink-0" />
              <span className="hidden xs:inline">返回主页</span>
              <span className="xs:hidden">返回</span>
            </Button>
          </Link>
          <h1 className="text-lg sm:text-2xl font-bold">个人中心</h1>
        </div>
        {/* Profile Header */}
        <div className="flex items-center gap-3 sm:gap-6 mb-6 sm:mb-8 p-4 sm:p-0 bg-card sm:bg-transparent rounded-lg sm:rounded-none border sm:border-0">
          <Avatar
            src={user.avatar_url}
            alt={user.username || user.email}
            size="lg"
            className="flex-shrink-0"
          />
          <div className="min-w-0 flex-1">
            <h2 className="text-lg sm:text-xl font-bold truncate">{user.username || "用户"}</h2>
            <p className="text-sm sm:text-base text-muted-foreground truncate">{user.email}</p>
          </div>
          <Link href="/profile/edit">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 sm:gap-2 touch-target px-2 sm:px-3"
            >
              <Edit className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden xs:inline">编辑资料</span>
              <span className="xs:hidden">编辑</span>
            </Button>
          </Link>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="liked" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 h-auto p-1 bg-muted rounded-lg">
            <TabsTrigger
              value="liked"
              className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-1.5 px-2 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-all touch-target"
            >
              <Heart className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden xs:inline sm:inline">我的点赞</span>
              <span className="xs:hidden sm:hidden">点赞</span>
            </TabsTrigger>
            <TabsTrigger
              value="favorites"
              className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-1.5 px-2 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-all touch-target"
            >
              <Bookmark className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden xs:inline sm:inline">我的收藏</span>
              <span className="xs:hidden sm:hidden">收藏</span>
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-1.5 px-2 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-all touch-target"
            >
              <Settings className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden xs:inline sm:inline">账户设置</span>
              <span className="xs:hidden sm:hidden">设置</span>
            </TabsTrigger>
          </TabsList>

          {/* Liked Works */}
          <TabsContent value="liked" className="space-y-4 sm:space-y-6">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 px-1">我点赞的作品</h2>
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-3 sm:mb-4"></div>
                      <div className="h-3 sm:h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-2 sm:h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ))}
                </div>
              ) : likedWorks.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                  {likedWorks.map((work) => (
                    <WorkCard key={work.id} work={work} onUpdate={fetchUserData} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 sm:py-12 px-4">
                  <Heart className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                  <p className="text-sm sm:text-base text-muted-foreground">还没有点赞任何作品</p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Favorite Works */}
          <TabsContent value="favorites" className="space-y-4 sm:space-y-6">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 px-1">我收藏的作品</h2>
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-3 sm:mb-4"></div>
                      <div className="h-3 sm:h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-2 sm:h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ))}
                </div>
              ) : favoriteWorks.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                  {favoriteWorks.map((work) => (
                    <WorkCard key={work.id} work={work} onUpdate={fetchUserData} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 sm:py-12 px-4">
                  <Bookmark className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                  <p className="text-sm sm:text-base text-muted-foreground">还没有收藏任何作品</p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings" className="space-y-4 sm:space-y-6">
            <Card className="border-0 sm:border shadow-none sm:shadow-sm">
              <CardHeader className="px-4 sm:px-6 pb-3 sm:pb-6">
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <Lock className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                  修改密码
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  为了账户安全，请定期更换密码
                </CardDescription>
              </CardHeader>
              <CardContent className="px-4 sm:px-6">
                <form onSubmit={handlePasswordChange} className="space-y-4 sm:space-y-5">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword" className="text-sm sm:text-base font-medium">
                      当前密码
                    </Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={passwordForm.currentPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                      required
                      className="h-11 sm:h-10 text-base sm:text-sm"
                      placeholder="请输入当前密码"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword" className="text-sm sm:text-base font-medium">
                      新密码
                    </Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={passwordForm.newPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                      required
                      minLength={6}
                      className="h-11 sm:h-10 text-base sm:text-sm"
                      placeholder="请输入新密码（至少6位）"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-sm sm:text-base font-medium">
                      确认新密码
                    </Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={passwordForm.confirmPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      required
                      minLength={6}
                      className="h-11 sm:h-10 text-base sm:text-sm"
                      placeholder="请再次输入新密码"
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full h-11 sm:h-10 text-base sm:text-sm font-medium touch-target mt-6"
                  >
                    修改密码
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
}
