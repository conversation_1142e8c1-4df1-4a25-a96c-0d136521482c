"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/lib/auth"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/toast"
import { apiClient } from "@/lib/api"
import { ArrowLeft, User, Save } from "lucide-react"
import Link from "next/link"
import { Avatar } from "@/components/avatar"

interface ProfileFormData {
  username: string
  avatar_url: string
}

export default function EditProfilePage() {
  const { user, refreshUser } = useAuth()
  const { showToast } = useToast()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<ProfileFormData>({
    username: "",
    avatar_url: ""
  })

  useEffect(() => {
    if (!user) {
      router.push("/")
      return
    }

    // 初始化表单数据
    setFormData({
      username: user.username || "",
      avatar_url: user.avatar_url || ""
    })
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      setLoading(true)

      // 只发送有变化的字段
      const updateData: Partial<ProfileFormData> = {}
      if (formData.username !== user.username) {
        updateData.username = formData.username
      }
      if (formData.avatar_url !== user.avatar_url) {
        updateData.avatar_url = formData.avatar_url
      }

      // 如果没有变化，直接返回
      if (Object.keys(updateData).length === 0) {
        showToast("没有需要更新的内容", "info")
        return
      }

      await apiClient.updateUserProfile(updateData)
      await refreshUser() // 刷新用户信息
      showToast("个人资料更新成功", "success")
      router.push("/profile")
    } catch (error) {
      console.error("Error updating profile:", error)
      showToast("更新失败，请重试", "error")
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return null
  }

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-2xl">
        {/* Header with Back Button */}
        <div className="flex items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          <Link href="/profile">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 sm:gap-2 touch-target px-2 sm:px-3"
            >
              <ArrowLeft className="h-4 w-4 flex-shrink-0" />
              <span className="hidden xs:inline">返回个人中心</span>
              <span className="xs:hidden">返回</span>
            </Button>
          </Link>
          <h1 className="text-lg sm:text-2xl font-bold">编辑个人资料</h1>
        </div>

        <Card className="border-0 sm:border shadow-none sm:shadow-sm">
          <CardHeader className="px-4 sm:px-6 pb-3 sm:pb-6">
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              个人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {/* 当前头像预览 */}
              <div className="flex items-center gap-4">
                <Avatar
                  src={formData.avatar_url}
                  alt={formData.username}
                  size="lg"
                  className="flex-shrink-0"
                />
                <div>
                  <p className="text-sm text-muted-foreground">当前头像</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    可以在下方修改头像链接
                  </p>
                </div>
              </div>

              {/* 用户名 */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm sm:text-base font-medium">
                  用户名
                </Label>
                <Input
                  id="username"
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  required
                  className="h-11 sm:h-10 text-base sm:text-sm"
                  placeholder="请输入用户名"
                />
              </div>

              {/* 头像链接 */}
              <div className="space-y-2">
                <Label htmlFor="avatar_url" className="text-sm sm:text-base font-medium">
                  头像链接
                </Label>
                <Input
                  id="avatar_url"
                  type="url"
                  value={formData.avatar_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, avatar_url: e.target.value }))}
                  className="h-11 sm:h-10 text-base sm:text-sm"
                  placeholder="请输入头像图片链接（可选）"
                />
                <p className="text-xs text-muted-foreground">
                  请输入有效的图片链接，如：https://example.com/avatar.jpg
                </p>
              </div>

              {/* 邮箱（只读） */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm sm:text-base font-medium">
                  邮箱地址
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={user.email}
                  disabled
                  className="h-11 sm:h-10 text-base sm:text-sm bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  邮箱地址无法修改
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex-1 h-11 sm:h-10 text-base sm:text-sm font-medium touch-target"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? "保存中..." : "保存更改"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/profile")}
                  className="h-11 sm:h-10 text-base sm:text-sm touch-target px-6"
                >
                  取消
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}
