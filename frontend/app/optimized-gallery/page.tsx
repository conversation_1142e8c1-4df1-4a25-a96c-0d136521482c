'use client';

import React, { useState, useEffect, useMemo } from 'react';
import OptimizedImage from '../../components/OptimizedImage';
import VirtualGrid from '../../components/VirtualGrid';
import PerformanceMonitor from '../../components/PerformanceMonitor';
import { useImagePreload } from '../../hooks/useImageLazyLoad';
import { getOptimizedImageUrl, imagePreloader, performanceMonitor } from '../../lib/performance';

interface Work {
  id: number;
  title: string;
  architect: string;
  year: number;
  award: string;
  cover_image: string;
  images: string[];
  description: string;
  tags: string[];
}

/**
 * 优化后的作品画廊页面
 * 展示所有性能优化技术的应用
 */
export default function OptimizedGallery() {
  const [works, setWorks] = useState<Work[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);
  const [selectedWork, setSelectedWork] = useState<Work | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'virtual'>('grid');

  // 获取作品数据
  useEffect(() => {
    const fetchWorks = async () => {
      try {
        const response = await fetch('/api/works?limit=100');
        const data = await response.json();
        setWorks(data.works || []);
        setTotalCount(data.total || 0);
      } catch (error) {
        console.error('Failed to fetch works:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWorks();
  }, []);

  // 预加载关键图片
  const priorityImages = useMemo(() => {
    return works.slice(0, 6).map(work => getOptimizedImageUrl(work.cover_image, 'medium'));
  }, [works]);

  const { preloadImages } = useImagePreload(priorityImages);

  // 启动性能监控
  useEffect(() => {
    performanceMonitor.start();
    
    // 预加载首屏图片
    if (priorityImages.length > 0) {
      preloadImages(priorityImages);
    }

    return () => {
      performanceMonitor.stop();
    };
  }, [priorityImages, preloadImages]);

  // 渲染作品卡片
  const renderWorkCard = (work: Work, index: number) => (
    <div
      key={work.id}
      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
      onClick={() => setSelectedWork(work)}
    >
      <div className="aspect-w-16 aspect-h-9 relative">
        <OptimizedImage
          src={work.cover_image}
          alt={work.title}
          width={400}
          height={225}
          className="w-full h-full object-cover"
          priority={index < 6} // 首屏图片优先加载
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        
        {/* 奖项标签 */}
        <div className="absolute top-2 left-2">
          <span
            data-award-badge
            className={`px-2.5 py-1 text-xs font-semibold rounded-full h-7 flex items-center justify-center whitespace-nowrap ${
              work.award === '一等奖' ? 'bg-yellow-500 text-white' :
              work.award === '二等奖' ? 'bg-gray-400 text-white' :
              work.award === '三等奖' ? 'bg-orange-500 text-white' :
              'bg-blue-500 text-white'
            }`}
            style={{ minWidth: 'auto', width: 'auto' }}
          >
            {work.award}
          </span>
        </div>
      </div>

      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {work.title}
        </h3>
        <p className="text-sm text-gray-600 mb-2">{work.architect}</p>
        <p className="text-xs text-gray-500">{work.year}</p>
        
        {/* 标签 */}
        <div className="mt-3 flex flex-wrap gap-1">
          {work.tags.map((tag, tagIndex) => (
            <span
              key={tagIndex}
              data-tag-badge
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full h-7 flex items-center justify-center whitespace-nowrap"
              style={{ minWidth: 'auto', width: 'auto' }}
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );

  // 处理性能指标更新
  const handlePerformanceUpdate = (metrics: any) => {
    console.log('Performance metrics:', metrics);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                霍普杯建筑设计竞赛作品
              </h1>
              <p className="mt-2 text-gray-600">
                共 {totalCount} 个获奖作品 · 性能优化版本
              </p>
            </div>

            {/* 控制面板 */}
            <div className="flex items-center space-x-4">
              {/* 视图模式切换 */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-1 text-sm rounded ${
                    viewMode === 'grid' 
                      ? 'bg-white text-gray-900 shadow' 
                      : 'text-gray-600'
                  }`}
                >
                  网格视图
                </button>
                <button
                  onClick={() => setViewMode('virtual')}
                  className={`px-3 py-1 text-sm rounded ${
                    viewMode === 'virtual' 
                      ? 'bg-white text-gray-900 shadow' 
                      : 'text-gray-600'
                  }`}
                >
                  虚拟滚动
                </button>
              </div>

              {/* 性能监控开关 */}
              <button
                onClick={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
                className={`px-4 py-2 text-sm font-medium rounded-lg ${
                  showPerformanceMonitor
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                性能监控
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {viewMode === 'grid' ? (
          /* 普通网格视图 */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {works.map((work, index) => renderWorkCard(work, index))}
          </div>
        ) : (
          /* 虚拟滚动视图 */
          <VirtualGrid
            items={works}
            itemWidth={300}
            itemHeight={400}
            containerHeight={800}
            gap={24}
            renderItem={renderWorkCard}
            className="border rounded-lg bg-white"
          />
        )}
      </main>

      {/* 作品详情模态框 */}
      {selectedWork && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-gray-900">
                  {selectedWork.title}
                </h2>
                <button
                  onClick={() => setSelectedWork(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 图片画廊 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {selectedWork.images.map((image, index) => (
                  <OptimizedImage
                    key={index}
                    src={image}
                    alt={`${selectedWork.title} - 图片 ${index + 1}`}
                    width={400}
                    height={300}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                ))}
              </div>

              {/* 作品信息 */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">作品信息</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">建筑师：</span>
                      <span className="text-gray-600">{selectedWork.architect}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">年份：</span>
                      <span className="text-gray-600">{selectedWork.year}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">奖项：</span>
                      <span className="text-gray-600">{selectedWork.award}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">作品描述</h3>
                  <p className="text-gray-600 leading-relaxed">
                    {selectedWork.description}
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">标签</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedWork.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 性能监控组件 */}
      <PerformanceMonitor
        showMetrics={showPerformanceMonitor}
        onMetricsUpdate={handlePerformanceUpdate}
      />
    </div>
  );
}
