"use client"

import type React from "react"
import { useState, useEffect, useRef, useCallback, memo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar } from "@/components/avatar"
import { useAuth } from "@/lib/auth"
import { AuthDialog } from "./auth-dialog"
import { useToast } from "./toast"
import { apiClient } from "@/lib/api"
import type { Comment } from "@/lib/types"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { MessageCircle, Reply } from "lucide-react"
import { ReplyInput } from "./reply-input"

interface CommentSectionProps {
  workId: string
}

// 回复状态接口
interface ReplyState {
  isReplying: boolean
  content: string
  isSubmitting: boolean
}

// CommentItem组件的props接口
interface CommentItemProps {
  comment: Comment
  depth?: number
  replyStates: Record<number, ReplyState>
  user: any
  onReplyClick: (commentId: number, e?: React.MouseEvent) => void
  onCancelReply: (commentId: number, e?: React.MouseEvent) => void
  onReplyContentChange: (commentId: number, content: string) => void
  onSubmitReply: (e: React.FormEvent, commentId: number) => void
}

// 独立的CommentItem组件
const CommentItem = ({
  comment,
  depth = 0,
  replyStates,
  user,
  onReplyClick,
  onCancelReply,
  onReplyContentChange,
  onSubmitReply
}: CommentItemProps) => {
  const replyState = replyStates[comment.id]
  const isReplying = replyState?.isReplying || false
  const maxDepth = 3 // 最大嵌套深度
  const textareaRef = useRef<HTMLTextAreaElement>(null)



  // 当回复状态变为true时，延迟聚焦到textarea
  useEffect(() => {
    if (isReplying && textareaRef.current) {
      // 使用setTimeout确保DOM已经更新
      const timer = setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus()
          // 将光标移到末尾
          const length = textareaRef.current.value.length
          textareaRef.current.setSelectionRange(length, length)
        }
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [isReplying])

  return (
    <div className={`${depth > 0 ? 'border-l-2 border-gray-100 pl-4' : ''}`}>
      <div className="flex gap-3">
        <Avatar
          src={comment.user_avatar}
          alt={comment.username}
          size="sm"
          className="shrink-0"
        />
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{comment.username}</span>
            {comment.reply_to_username && (
              <span className="text-xs text-muted-foreground">
                回复 @{comment.reply_to_username}
              </span>
            )}
            <span className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(comment.created_at), {
                addSuffix: true,
                locale: zhCN,
              })}
            </span>
          </div>
          <p className="text-sm mb-2">{comment.content}</p>


          {/* 回复按钮 */}
          {user && depth < maxDepth && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                onClick={(e) => onReplyClick(comment.id, e)}
                type="button"
              >
                <Reply className="h-3 w-3 mr-1" />
                回复
              </Button>
              {comment.replies && comment.replies.length > 0 && (
                <span className="text-xs text-muted-foreground">
                  {comment.replies.length} 条回复
                </span>
              )}
            </div>
          )}

          {/* 回复表单 */}
          {isReplying && (
            <div className="mt-3 space-y-2">
              <form onSubmit={(e) => onSubmitReply(e, comment.id)} className="space-y-2">
                <div className="flex gap-2">
                  <Avatar
                    src={user.user_metadata?.avatar_url}
                    alt={user.user_metadata?.username || user.email}
                    size="sm"
                    className="shrink-0"
                  />
                  <div className="flex-1">
                    <Textarea
                      ref={textareaRef}
                      placeholder={`回复 @${comment.username}...`}
                      value={replyState?.content || ""}
                      onChange={(e) => onReplyContentChange(comment.id, e.target.value)}
                      className="min-h-[60px] text-sm"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={(e) => onCancelReply(comment.id, e)}
                  >
                    取消
                  </Button>
                  <Button
                    type="submit"
                    size="sm"
                    disabled={replyState?.isSubmitting || !replyState?.content.trim()}
                  >
                    {replyState?.isSubmitting ? "发布中..." : "发布回复"}
                  </Button>
                </div>
              </form>
            </div>
          )}

          {/* 嵌套回复 */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3 space-y-3">
              {comment.replies.map((reply) => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  depth={depth + 1}
                  replyStates={replyStates}
                  user={user}
                  onReplyClick={onReplyClick}
                  onCancelReply={onCancelReply}
                  onReplyContentChange={onReplyContentChange}
                  onSubmitReply={onSubmitReply}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export function CommentSection({ workId }: CommentSectionProps) {
  const { user } = useAuth()
  const { showToast } = useToast()
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loading, setLoading] = useState(true)
  // 管理每个评论的回复状态
  const [replyStates, setReplyStates] = useState<Record<number, ReplyState>>({})

  const fetchComments = async () => {
    try {
      const data = await apiClient.getComments(workId)
      console.log("获取到的评论数据:", data)

      // 检查数据是否已经是嵌套结构
      if (data && data.length > 0 && data[0].replies !== undefined) {
        // 后端已经返回嵌套结构，直接使用
        setComments(data)
      } else {
        // 后端返回扁平结构，需要组织为嵌套结构
        const organizedComments = organizeComments(data)
        setComments(organizedComments)
      }
    } catch (error) {
      console.error("Error fetching comments:", error)
    } finally {
      setLoading(false)
    }
  }

  // 将扁平的评论列表组织为嵌套结构
  const organizeComments = (flatComments: Comment[]): Comment[] => {
    const commentMap = new Map<number, Comment>()
    const rootComments: Comment[] = []

    // 首先创建所有评论的映射
    flatComments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] })
    })

    // 然后组织父子关系
    flatComments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!
      if (comment.parent_id) {
        const parent = commentMap.get(comment.parent_id)
        if (parent) {
          parent.replies = parent.replies || []
          parent.replies.push(commentWithReplies)
        }
      } else {
        rootComments.push(commentWithReplies)
      }
    })

    return rootComments
  }

  useEffect(() => {
    fetchComments()
  }, [workId])

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newComment.trim()) return

    setIsSubmitting(true)

    try {
      await apiClient.createComment(workId, {
        content: newComment.trim(),
      })
      setNewComment("")
      showToast("评论已提交，等待管理员审核后显示", "success")
      // 重新获取评论列表
      await fetchComments()
    } catch (error) {
      console.error("Error submitting comment:", error)
      showToast("评论发布失败，请重试", "error")
    } finally {
      setIsSubmitting(false)
    }
  }



  const handleReplyClick = useCallback((commentId: number, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    if (!user) return

    setReplyStates(prev => ({
      ...prev,
      [commentId]: {
        isReplying: true,
        content: "",
        isSubmitting: false
      }
    }))
  }, [user])

  const handleCancelReply = useCallback((commentId: number, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    setReplyStates(prev => ({
      ...prev,
      [commentId]: {
        isReplying: false,
        content: "",
        isSubmitting: false
      }
    }))
  }, [])

  const handleReplyContentChange = useCallback((commentId: number, content: string) => {
    setReplyStates(prev => ({
      ...prev,
      [commentId]: {
        ...prev[commentId],
        content
      }
    }))
  }, [])

  const handleSubmitReply = async (e: React.FormEvent, commentId: number) => {
    e.preventDefault()
    if (!user) return

    const replyState = replyStates[commentId]
    if (!replyState?.content.trim()) return

    setReplyStates(prev => ({
      ...prev,
      [commentId]: {
        ...prev[commentId],
        isSubmitting: true
      }
    }))

    try {
      await apiClient.createComment(workId, {
        content: replyState.content.trim(),
        parent_id: commentId,
      })

      setReplyStates(prev => ({
        ...prev,
        [commentId]: {
          isReplying: false,
          content: "",
          isSubmitting: false
        }
      }))

      showToast("回复已提交，等待管理员审核后显示", "success")
      // 重新获取评论列表
      await fetchComments()
    } catch (error) {
      console.error("Error submitting reply:", error)
      showToast("回复发布失败，请重试", "error")
      setReplyStates(prev => ({
        ...prev,
        [commentId]: {
          ...prev[commentId],
          isSubmitting: false
        }
      }))
    }
  }



  // 计算总评论数（包括回复）
  const getTotalCommentCount = (comments: Comment[]): number => {
    return comments.reduce((total, comment) => {
      return total + 1 + (comment.replies ? getTotalCommentCount(comment.replies) : 0)
    }, 0)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="bg-gray-200 h-6 rounded mb-4"></div>
          <div className="bg-gray-200 h-20 rounded"></div>
        </div>
      </div>
    )
  }

  const totalComments = getTotalCommentCount(comments)

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <MessageCircle className="h-5 w-5" />
        <h3 className="text-lg font-semibold">评论 ({totalComments})</h3>
      </div>

      {/* Comment Form */}
      {user ? (
        <form onSubmit={handleSubmitComment} className="space-y-4">
          <div className="flex gap-3">
            <Avatar
              src={user.user_metadata?.avatar_url}
              alt={user.user_metadata?.username || user.email}
              size="sm"
              className="shrink-0"
            />
            <div className="flex-1">
              <Textarea
                placeholder="写下你的评论..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting || !newComment.trim()}>
              {isSubmitting ? "发布中..." : "发布评论"}
            </Button>
          </div>
        </form>
      ) : (
        <div className="border rounded-lg p-4 text-center">
          <p className="text-muted-foreground mb-3">登录后即可发表评论和回复</p>
          <AuthDialog>
            <Button>登录/注册</Button>
          </AuthDialog>
        </div>
      )}

      {/* Comments List */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>还没有评论，来发表第一条评论吧！</p>
          </div>
        ) : (
          comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              replyStates={replyStates}
              user={user}
              onReplyClick={handleReplyClick}
              onCancelReply={handleCancelReply}
              onReplyContentChange={handleReplyContentChange}
              onSubmitReply={handleSubmitReply}
            />
          ))
        )}
      </div>
    </div>
  )
}
