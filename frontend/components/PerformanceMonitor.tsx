import React, { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  memoryUsage?: {
    used: number;
    total: number;
  };
  networkInfo?: {
    effectiveType: string;
    downlink: number;
  };
}

interface PerformanceMonitorProps {
  showMetrics?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

/**
 * 性能监控组件
 * 功能：
 * 1. 监控页面加载性能
 * 2. 收集Core Web Vitals指标
 * 3. 监控内存使用情况
 * 4. 网络状态检测
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  showMetrics = false,
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(showMetrics);

  // 获取性能指标
  const collectMetrics = (): PerformanceMetrics | null => {
    if (typeof window === 'undefined' || !window.performance) {
      return null;
    }

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    let firstContentfulPaint = 0;
    const fcpEntry = paint.find(entry => entry.name === 'first-contentful-paint');
    if (fcpEntry) {
      firstContentfulPaint = fcpEntry.startTime;
    }

    // 获取LCP (需要PerformanceObserver)
    let largestContentfulPaint = 0;
    
    // 获取内存使用情况
    let memoryUsage;
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      memoryUsage = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }

    // 获取网络信息
    let networkInfo;
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      networkInfo = {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0
      };
    }

    return {
      loadTime: navigation.loadEventEnd - navigation.navigationStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      firstContentfulPaint,
      largestContentfulPaint,
      cumulativeLayoutShift: 0, // 需要PerformanceObserver
      firstInputDelay: 0, // 需要PerformanceObserver
      memoryUsage,
      networkInfo
    };
  };

  // 设置Core Web Vitals观察器
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let lcpObserver: PerformanceObserver | null = null;
    let clsObserver: PerformanceObserver | null = null;
    let fidObserver: PerformanceObserver | null = null;

    // LCP观察器
    if ('PerformanceObserver' in window) {
      try {
        lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          setMetrics(prev => prev ? {
            ...prev,
            largestContentfulPaint: lastEntry.startTime
          } : null);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // CLS观察器
        clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          setMetrics(prev => prev ? {
            ...prev,
            cumulativeLayoutShift: clsValue
          } : null);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

        // FID观察器
        fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            setMetrics(prev => prev ? {
              ...prev,
              firstInputDelay: (entry as any).processingStart - entry.startTime
            } : null);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (error) {
        console.warn('PerformanceObserver not supported:', error);
      }
    }

    return () => {
      lcpObserver?.disconnect();
      clsObserver?.disconnect();
      fidObserver?.disconnect();
    };
  }, []);

  // 初始化性能监控
  useEffect(() => {
    const timer = setTimeout(() => {
      const initialMetrics = collectMetrics();
      if (initialMetrics) {
        setMetrics(initialMetrics);
        onMetricsUpdate?.(initialMetrics);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [onMetricsUpdate]);

  // 定期更新指标
  useEffect(() => {
    const interval = setInterval(() => {
      const updatedMetrics = collectMetrics();
      if (updatedMetrics) {
        setMetrics(prev => prev ? { ...prev, ...updatedMetrics } : updatedMetrics);
        onMetricsUpdate?.(updatedMetrics);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [onMetricsUpdate]);

  // 格式化时间
  const formatTime = (time: number): string => {
    if (time < 1000) {
      return `${Math.round(time)}ms`;
    }
    return `${(time / 1000).toFixed(2)}s`;
  };

  // 获取性能评级
  const getPerformanceGrade = (metric: string, value: number): string => {
    switch (metric) {
      case 'lcp':
        if (value <= 2500) return 'good';
        if (value <= 4000) return 'needs-improvement';
        return 'poor';
      case 'fid':
        if (value <= 100) return 'good';
        if (value <= 300) return 'needs-improvement';
        return 'poor';
      case 'cls':
        if (value <= 0.1) return 'good';
        if (value <= 0.25) return 'needs-improvement';
        return 'poor';
      default:
        return 'unknown';
    }
  };

  // 获取评级颜色
  const getGradeColor = (grade: string): string => {
    switch (grade) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (!showMetrics || !metrics) {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 transition-all duration-300 ${
      isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
    }`}>
      <div className="bg-white rounded-lg shadow-lg border p-4 max-w-sm">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-800">性能监控</h3>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        <div className="space-y-2 text-xs">
          {/* 页面加载时间 */}
          <div className="flex justify-between">
            <span className="text-gray-600">页面加载:</span>
            <span className="font-mono">{formatTime(metrics.loadTime)}</span>
          </div>

          {/* DOM加载时间 */}
          <div className="flex justify-between">
            <span className="text-gray-600">DOM加载:</span>
            <span className="font-mono">{formatTime(metrics.domContentLoaded)}</span>
          </div>

          {/* Core Web Vitals */}
          <div className="border-t pt-2 mt-2">
            <div className="text-gray-700 font-medium mb-1">Core Web Vitals</div>
            
            {/* LCP */}
            <div className="flex justify-between">
              <span className="text-gray-600">LCP:</span>
              <span className={`font-mono ${getGradeColor(getPerformanceGrade('lcp', metrics.largestContentfulPaint))}`}>
                {formatTime(metrics.largestContentfulPaint)}
              </span>
            </div>

            {/* FID */}
            <div className="flex justify-between">
              <span className="text-gray-600">FID:</span>
              <span className={`font-mono ${getGradeColor(getPerformanceGrade('fid', metrics.firstInputDelay))}`}>
                {formatTime(metrics.firstInputDelay)}
              </span>
            </div>

            {/* CLS */}
            <div className="flex justify-between">
              <span className="text-gray-600">CLS:</span>
              <span className={`font-mono ${getGradeColor(getPerformanceGrade('cls', metrics.cumulativeLayoutShift))}`}>
                {metrics.cumulativeLayoutShift.toFixed(3)}
              </span>
            </div>
          </div>

          {/* 内存使用 */}
          {metrics.memoryUsage && (
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-gray-600">内存:</span>
                <span className="font-mono">
                  {metrics.memoryUsage.used}MB / {metrics.memoryUsage.total}MB
                </span>
              </div>
            </div>
          )}

          {/* 网络信息 */}
          {metrics.networkInfo && (
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-gray-600">网络:</span>
                <span className="font-mono">
                  {metrics.networkInfo.effectiveType} ({metrics.networkInfo.downlink}Mbps)
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 切换按钮 */}
      {!isVisible && (
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-2 shadow-lg"
          title="显示性能监控"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default PerformanceMonitor;
