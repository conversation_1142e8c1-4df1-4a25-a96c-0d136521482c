import React from "react"
import { cn } from "@/lib/utils"

interface LogoProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export function Logo({ className, size = "md" }: LogoProps) {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  }

  return (
    <svg
      className={cn(sizeClasses[size], className)}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 建筑轮廓 */}
      <path
        d="M4 28V12L16 4L28 12V28H20V20H12V28H4Z"
        fill="currentColor"
        fillOpacity="0.1"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinejoin="round"
      />
      
      {/* 门 */}
      <rect
        x="14"
        y="22"
        width="4"
        height="6"
        fill="currentColor"
        fillOpacity="0.3"
      />
      
      {/* 窗户 */}
      <rect
        x="8"
        y="16"
        width="3"
        height="3"
        fill="currentColor"
        fillOpacity="0.5"
      />
      <rect
        x="21"
        y="16"
        width="3"
        height="3"
        fill="currentColor"
        fillOpacity="0.5"
      />
      
      {/* 奖杯元素 */}
      <circle
        cx="16"
        cy="10"
        r="2"
        fill="currentColor"
        fillOpacity="0.8"
      />
    </svg>
  )
}
