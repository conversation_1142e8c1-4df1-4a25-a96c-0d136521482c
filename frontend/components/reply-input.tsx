"use client"

import React, { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar } from "@/components/avatar"
import { useAuth } from "@/lib/auth"

interface ReplyInputProps {
  commentId: number
  replyToUsername: string
  onSubmit: (content: string) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
}

export function ReplyInput({ 
  commentId, 
  replyToUsername, 
  onSubmit, 
  onCancel, 
  isSubmitting = false 
}: ReplyInputProps) {
  const { user } = useAuth()
  const [content, setContent] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // 组件挂载时聚焦
  useEffect(() => {
    const timer = setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
        // 确保光标在末尾
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
      }
    }, 50)
    
    return () => clearTimeout(timer)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return
    
    try {
      await onSubmit(content.trim())
      setContent("") // 清空内容
    } catch (error) {
      // 错误处理由父组件负责
    }
  }

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setContent("")
    onCancel()
  }

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value)
  }

  if (!user) return null

  return (
    <div className="mt-3 space-y-2">
      <form onSubmit={handleSubmit} className="space-y-2">
        <div className="flex gap-2">
          <Avatar
            src={user.user_metadata?.avatar_url}
            alt={user.user_metadata?.username || user.email}
            size="sm"
            className="shrink-0"
          />
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              placeholder={`回复 @${replyToUsername}...`}
              value={content}
              onChange={handleContentChange}
              className="min-h-[60px] text-sm"
              disabled={isSubmitting}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            取消
          </Button>
          <Button
            type="submit"
            size="sm"
            disabled={isSubmitting || !content.trim()}
          >
            {isSubmitting ? "发布中..." : "发布回复"}
          </Button>
        </div>
      </form>
    </div>
  )
}
