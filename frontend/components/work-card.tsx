"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"
import { Eye, Award } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import type { Work } from "@/lib/types"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { useScrollPosition } from "@/lib/scroll-position"

interface WorkCardProps {
  work: Work
  onUpdate?: () => void
}

export function WorkCard({ work, onUpdate }: WorkCardProps) {
  const isMobile = useIsMobile()
  const { saveCurrentPosition } = useScrollPosition('home-page')

  const handleClick = () => {
    saveCurrentPosition()
  }

  const getAwardColor = (award: string) => {
    switch (award) {
      case "一等奖":
        return "bg-red-500"
      case "二等奖":
        return "bg-orange-500"
      case "三等奖":
        return "bg-amber-500"
      case "优秀奖":
        return "bg-blue-500"
      case "入围奖":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <Link href={`/work/${work.id}`} className="group block" onClick={handleClick}>
      <div className="relative overflow-hidden rounded-lg border bg-card">
        {/* Image */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={work.cover_image || "/placeholder.svg"}
            alt={work.title}
            fill
            className="object-cover transition-transform group-hover:scale-105"
          />

          {/* Award Badge */}
          <div className="absolute top-2 left-2 sm:top-3 sm:left-3">
            <Badge
              data-award-badge
              className={cn(
                "text-white font-medium border-0",
                getAwardColor(work.award),
                isMobile ? "text-[10px] px-1.5 py-0.5 h-5" : "text-xs px-2.5 py-1 h-7",
                "flex items-center justify-center whitespace-nowrap inline-flex"
              )}
              style={isMobile ? { minWidth: 'auto', width: 'auto' } : {}}
            >
              <Award className={cn("mr-0.5 flex-shrink-0", isMobile ? "h-2 w-2" : "h-3 w-3")} />
              <span className="whitespace-nowrap">{work.award}</span>
            </Badge>
          </div>

          {/* Stats Overlay */}
          <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex items-center gap-2">
            <div className="flex items-center gap-1 rounded bg-black/50 text-white px-2 py-1">
              <Eye className="h-3 w-3" />
              <span className="text-xs">{work.views_count}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className={cn("p-3 sm:p-4")}>
          <div className="mb-2">
            <h3 className="font-semibold line-clamp-1 text-sm sm:text-base">{work.title}</h3>
            <p className="text-muted-foreground text-xs sm:text-sm">{work.architect}</p>
          </div>

          <p className="text-muted-foreground line-clamp-2 mb-3 text-xs sm:text-sm">{work.description}</p>

          <div className="flex items-center justify-between text-muted-foreground text-xs">
            <span>{work.year}年</span>
            {work.location && <span className="truncate ml-2">{work.location}</span>}
          </div>

          {/* Tags */}
          {work.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {work.tags.map((tag) => (
                <Badge
                  key={tag}
                  data-tag-badge
                  variant="secondary"
                  className={cn(
                    "border-0 bg-gray-100 text-gray-700 font-normal",
                    isMobile ? "text-[10px] px-1.5 py-0.5 h-4" : "text-xs px-2 py-0.5 h-6",
                    "flex items-center justify-center whitespace-nowrap inline-flex"
                  )}
                  style={isMobile ? { minWidth: 'auto', width: 'auto' } : {}}
                >
                  <span className="whitespace-nowrap">{tag}</span>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </div>
    </Link>
  )
}
