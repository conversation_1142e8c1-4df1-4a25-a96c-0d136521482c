import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';

interface VirtualGridProps<T> {
  items: T[];
  itemHeight: number;
  itemWidth: number;
  containerHeight: number;
  gap?: number;
  overscan?: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  onScroll?: (scrollTop: number) => void;
  className?: string;
}

/**
 * 虚拟滚动网格组件
 * 功能：
 * 1. 只渲染可见区域的元素
 * 2. 支持大量数据的高性能展示
 * 3. 自动计算网格布局
 * 4. 平滑滚动体验
 */
function VirtualGrid<T>({
  items,
  itemHeight,
  itemWidth,
  containerHeight,
  gap = 16,
  overscan = 5,
  renderItem,
  onScroll,
  className = ''
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // 计算每行可以放置的列数
  const columnsPerRow = useMemo(() => {
    if (containerWidth === 0) return 1;
    return Math.floor((containerWidth + gap) / (itemWidth + gap));
  }, [containerWidth, itemWidth, gap]);

  // 计算总行数
  const totalRows = useMemo(() => {
    return Math.ceil(items.length / columnsPerRow);
  }, [items.length, columnsPerRow]);

  // 计算总高度
  const totalHeight = useMemo(() => {
    return totalRows * (itemHeight + gap) - gap;
  }, [totalRows, itemHeight, gap]);

  // 计算可见区域的行范围
  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / (itemHeight + gap));
    const endRow = Math.min(
      totalRows - 1,
      Math.ceil((scrollTop + containerHeight) / (itemHeight + gap))
    );

    return {
      start: Math.max(0, startRow - overscan),
      end: Math.min(totalRows - 1, endRow + overscan)
    };
  }, [scrollTop, containerHeight, itemHeight, gap, totalRows, overscan]);

  // 计算可见的项目
  const visibleItems = useMemo(() => {
    const items_to_render = [];
    
    for (let row = visibleRange.start; row <= visibleRange.end; row++) {
      for (let col = 0; col < columnsPerRow; col++) {
        const index = row * columnsPerRow + col;
        if (index < items.length) {
          const item = items[index];
          const x = col * (itemWidth + gap);
          const y = row * (itemHeight + gap);
          
          items_to_render.push({
            item,
            index,
            x,
            y
          });
        }
      }
    }
    
    return items_to_render;
  }, [items, visibleRange, columnsPerRow, itemWidth, itemHeight, gap]);

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  // 监听容器宽度变化
  useEffect(() => {
    const updateContainerWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    updateContainerWidth();

    const resizeObserver = new ResizeObserver(updateContainerWidth);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 滚动到指定项目
  const scrollToItem = useCallback((index: number) => {
    if (!scrollElementRef.current) return;

    const row = Math.floor(index / columnsPerRow);
    const targetScrollTop = row * (itemHeight + gap);
    
    scrollElementRef.current.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    });
  }, [columnsPerRow, itemHeight, gap]);

  // 暴露滚动方法
  React.useImperativeHandle(containerRef, () => ({
    scrollToItem,
    scrollToTop: () => {
      scrollElementRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
    },
    scrollToBottom: () => {
      scrollElementRef.current?.scrollTo({ top: totalHeight, behavior: 'smooth' });
    }
  }));

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ height: containerHeight }}
    >
      <div
        ref={scrollElementRef}
        className="w-full h-full overflow-auto"
        onScroll={handleScroll}
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#cbd5e0 #f7fafc'
        }}
      >
        {/* 虚拟容器 */}
        <div
          className="relative"
          style={{
            height: totalHeight,
            width: '100%'
          }}
        >
          {/* 渲染可见项目 */}
          {visibleItems.map(({ item, index, x, y }) => (
            <div
              key={index}
              className="absolute"
              style={{
                left: x,
                top: y,
                width: itemWidth,
                height: itemHeight
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>

      {/* 滚动指示器 */}
      {totalHeight > containerHeight && (
        <div className="absolute right-2 top-2 bottom-2 w-1 bg-gray-200 rounded-full opacity-50">
          <div
            className="bg-gray-400 rounded-full transition-all duration-150"
            style={{
              height: `${(containerHeight / totalHeight) * 100}%`,
              transform: `translateY(${(scrollTop / (totalHeight - containerHeight)) * (containerHeight - (containerHeight / totalHeight) * containerHeight)}px)`
            }}
          />
        </div>
      )}
    </div>
  );
}

export default VirtualGrid;
