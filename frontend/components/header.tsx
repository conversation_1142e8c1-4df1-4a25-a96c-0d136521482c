"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { Calendar, Search, Menu, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar } from "@/components/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { awardLevels } from "@/lib/data"
import { useAuth } from "@/lib/auth"
import { AuthDialog } from "./auth-dialog"
import { cn } from "@/lib/utils"
import { User, LogOut, Award, Settings } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useIsMobile } from "@/hooks/use-mobile"
import { Logo } from "./logo"

interface HeaderProps {
  selectedYear: string
  selectedAward: string
  onYearChange: (year: string) => void
  onAwardChange: (award: string) => void
  onSearch: (query: string) => void
}

export function Header({
  selectedYear,
  selectedAward,
  onYearChange,
  onAwardChange,
  onSearch,
}: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [location, setLocation] = useState("Entrez une adresse")
  const [date, setDate] = useState<Date>()
  const [address, setAddress] = useState("")
  const [isAddressOpen, setIsAddressOpen] = useState(false)
  const [isDateOpen, setIsDateOpen] = useState(false)
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)

  const { user, logout } = useAuth()
  const isMobile = useIsMobile()
  const router = useRouter()

  const handleLogoClick = () => {
    router.push('/')
    router.refresh()
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery)
  }

  const years = ["全部", "2024", "2023", "2022", "2021"]

  // Geolocation
  useEffect(() => {
    const checkGeolocation = async () => {
      // 检查是否为安全上下文（HTTPS或localhost）
      if (!window.isSecureContext) {
        console.warn("地理位置API需要安全上下文（HTTPS）")
        setLocation("请输入地址")
        return
      }

      if ("geolocation" in navigator) {
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: false, // 降低精度要求
              timeout: 10000, // 增加超时时间
              maximumAge: 300000, // 5分钟内的缓存位置可用
            })
          })
          setLocation("附近位置")
        } catch (error) {
          console.warn("地理位置获取失败:", error)
          setLocation("请输入地址")
        }
      } else {
        setLocation("请输入地址")
      }
    }

    checkGeolocation()
  }, [])

  return (
    <header className="sticky top-0 z-10 bg-background border-b">
      <div className="container mx-auto px-2 sm:px-4">
        {/* Top Bar */}
        <div className="flex items-center justify-between h-14 sm:h-16">
          <div className="flex items-center gap-2 sm:gap-3 flex-1 min-w-0">
            <button
              onClick={handleLogoClick}
              className="flex items-center gap-2 hover:opacity-80 transition-opacity"
            >
              <Logo size={isMobile ? "sm" : "md"} className="text-primary" />
              <h1 className="text-sm sm:text-xl font-bold truncate">
                {isMobile ? "UIA 霍普杯" : "UIA 霍普杯国际大学生建筑设计竞赛-2024线上作品巡展"}
              </h1>
            </button>
          </div>

          {/* Action Icons */}
          <div className="flex items-center gap-1">
            {/* Search/Filter Toggle Icon */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsFiltersOpen(!isFiltersOpen)}
              className="p-2"
            >
              {isFiltersOpen ? <Filter className="h-5 w-5" /> : <Search className="h-5 w-5" />}
            </Button>

            {/* User Menu */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="p-2">
                    <Avatar
                      src={user?.avatar_url}
                      alt={user?.username || user?.email}
                      size="sm"
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/profile">
                      <User className="mr-2 h-4 w-4" />
                      个人中心
                    </Link>
                  </DropdownMenuItem>
                  {user?.is_admin && (
                    <DropdownMenuItem asChild>
                      <Link href="/admin">
                        <Settings className="mr-2 h-4 w-4" />
                        管理后台
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={logout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <AuthDialog>
                <Button variant="ghost" size="sm" className="p-2">
                  <User className="h-5 w-5" />
                </Button>
              </AuthDialog>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className={cn(
          "border-t overflow-hidden transition-all duration-300 ease-in-out",
          isFiltersOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        )}>
          {!isMobile ? (
            /* Desktop Layout - All in one row */
            <div className="p-4">
              <div className="flex items-center gap-4">
                {/* Year Filter */}
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedYear} onValueChange={onYearChange}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Award Filter */}
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedAward} onValueChange={onAwardChange}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="获奖等级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="全部">全部</SelectItem>
                      {awardLevels.map((award) => (
                        <SelectItem key={award} value={award}>
                          {award}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Search */}
                <form onSubmit={handleSearch} className="flex items-center gap-2 flex-1 max-w-md">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="搜索作品、建筑师..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-9"
                    />
                  </div>
                  <Button type="submit" size="sm">
                    搜索
                  </Button>
                </form>
              </div>
            </div>
          ) : (
            /* Mobile Layout - Filters first, search below */
            <div className="p-4 space-y-4">
              {/* Filters Row */}
              <div className="flex items-center gap-3">
                {/* Year Filter */}
                <div className="flex items-center gap-2 flex-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedYear} onValueChange={onYearChange}>
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Award Filter */}
                <div className="flex items-center gap-2 flex-1">
                  <Award className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedAward} onValueChange={onAwardChange}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="获奖等级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="全部">全部</SelectItem>
                      {awardLevels.map((award) => (
                        <SelectItem key={award} value={award}>
                          {award}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Search */}
              <form onSubmit={handleSearch} className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                      placeholder="搜索作品、建筑师..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-9"
                  />
                </div>
                <Button type="submit" size="sm">
                  搜索
                </Button>
              </form>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
