import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  fill?: boolean;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

/**
 * 优化的图片组件
 * 功能：
 * 1. 自动选择最佳图片尺寸
 * 2. 支持WebP格式
 * 3. 懒加载和预加载
 * 4. 加载状态和错误处理
 * 5. 响应式图片
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 85,
  sizes,
  fill = false,
  style,
  onLoad,
  onError,
  placeholder = 'blur',
  blurDataURL
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [optimizedSrc, setOptimizedSrc] = useState(src);
  const imgRef = useRef<HTMLImageElement>(null);

  // 生成优化后的图片路径
  const getOptimizedImageSrc = (originalSrc: string, targetWidth?: number): string => {
    if (!originalSrc) return originalSrc;

    // 如果是外部链接，直接返回
    if (originalSrc.startsWith('http')) {
      return originalSrc;
    }

    // 检查是否是作品图片
    if (originalSrc.includes('/uploads/works/')) {
      const fileName = originalSrc.split('/').pop();
      if (fileName) {
        const baseName = fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
        
        // 根据目标宽度选择合适的尺寸
        let sizeType = 'medium';
        if (targetWidth) {
          if (targetWidth <= 300) {
            sizeType = 'thumbnail';
          } else if (targetWidth <= 800) {
            sizeType = 'medium';
          } else if (targetWidth <= 1200) {
            sizeType = 'large';
          } else {
            sizeType = 'original';
          }
        }

        // 生成优化后的路径
        const optimizedPath = `/uploads/works_optimized/${baseName}_${sizeType}.webp`;
        return optimizedPath;
      }
    }

    return originalSrc;
  };

  // 生成模糊占位符
  const generateBlurDataURL = (): string => {
    if (blurDataURL) return blurDataURL;
    
    // 生成简单的模糊占位符
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmM2Y0ZjYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNlNWU3ZWIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2cpIi8+PC9zdmc+';
  };

  // 检查WebP支持
  const checkWebPSupport = (): Promise<boolean> => {
    return new Promise((resolve) => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  };

  // 初始化优化图片路径
  useEffect(() => {
    const initOptimizedSrc = async () => {
      const supportsWebP = await checkWebPSupport();
      
      if (supportsWebP) {
        const optimized = getOptimizedImageSrc(src, width);
        setOptimizedSrc(optimized);
      } else {
        setOptimizedSrc(src);
      }
    };

    initOptimizedSrc();
  }, [src, width]);

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  // 处理图片加载错误
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    
    // 如果优化图片加载失败，回退到原图
    if (optimizedSrc !== src) {
      setOptimizedSrc(src);
      setHasError(false);
      return;
    }
    
    onError?.();
  };

  // 生成响应式sizes属性
  const getResponsiveSizes = (): string => {
    if (sizes) return sizes;
    
    if (width) {
      return `(max-width: 768px) ${Math.min(width, 400)}px, ${width}px`;
    }
    
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
  };

  // 加载状态组件
  const LoadingPlaceholder = () => (
    <div 
      className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
      style={{ 
        width: fill ? '100%' : width, 
        height: fill ? '100%' : height,
        ...style 
      }}
    >
      <svg 
        className="w-8 h-8 text-gray-400" 
        fill="currentColor" 
        viewBox="0 0 20 20"
      >
        <path 
          fillRule="evenodd" 
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
          clipRule="evenodd" 
        />
      </svg>
    </div>
  );

  // 错误状态组件
  const ErrorPlaceholder = () => (
    <div 
      className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center ${className}`}
      style={{ 
        width: fill ? '100%' : width, 
        height: fill ? '100%' : height,
        ...style 
      }}
    >
      <div className="text-center text-gray-500">
        <svg 
          className="w-8 h-8 mx-auto mb-2" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path 
            fillRule="evenodd" 
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
            clipRule="evenodd" 
          />
        </svg>
        <p className="text-sm">图片加载失败</p>
      </div>
    </div>
  );

  // 如果有错误且回退也失败，显示错误占位符
  if (hasError) {
    return <ErrorPlaceholder />;
  }

  return (
    <div className={`relative ${isLoading ? 'overflow-hidden' : ''}`}>
      {/* 加载状态 */}
      {isLoading && <LoadingPlaceholder />}
      
      {/* 优化后的图片 */}
      <Image
        ref={imgRef}
        src={optimizedSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        style={style}
        priority={priority}
        quality={quality}
        sizes={getResponsiveSizes()}
        placeholder={placeholder}
        blurDataURL={generateBlurDataURL()}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? 'eager' : 'lazy'}
      />
    </div>
  );
};

export default OptimizedImage;
