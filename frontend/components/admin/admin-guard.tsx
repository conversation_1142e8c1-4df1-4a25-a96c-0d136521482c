"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/auth"
import { AdminLayout } from "./admin-layout"

interface AdminGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AdminGuard({ children, fallback }: AdminGuardProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        // 用户未登录，重定向到首页
        router.push("/")
        return
      }
      
      if (!user.is_admin) {
        // 用户已登录但不是管理员，重定向到首页
        router.push("/")
        return
      }
      
      // 用户是管理员，允许访问
      setIsChecking(false)
    }
  }, [user, isLoading, router])

  // 正在检查身份验证状态
  if (isLoading || isChecking) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-600">正在验证身份...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  // 用户未登录或不是管理员
  if (!user || !user.is_admin) {
    return fallback || (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
            <p className="text-gray-600">您没有权限访问此页面。</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  // 用户是管理员，显示内容
  return <>{children}</>
}
