/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 移除 standalone 配置，因为与 next start 不兼容

  // 处理反向代理的配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ]
  },

  // 重写规则（如果需要）
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*', // 保持API路由不变
      },
    ]
  },
}

export default nextConfig
