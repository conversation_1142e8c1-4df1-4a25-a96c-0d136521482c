# 环境变量配置说明

## 🎯 统一环境变量管理

为了避免环境变量重复定义和配置冲突，现在采用统一的环境变量管理方案。

## 📁 配置文件说明

### 1. 主要配置文件

| 文件 | 用途 | 优先级 |
|------|------|--------|
| `env-unified.sh` | **运行时统一配置** | 🔥 最高 |
| `1panel/.env` | 1Panel界面显示和构建参考 | 低 |
| `.env.production` | Next.js构建时配置 | 中 |
| `.env.local` | 本地开发配置 | 中 |

### 2. 配置优先级

```
容器运行时: env-unified.sh (最高优先级)
    ↓
Next.js构建: .env.production
    ↓
本地开发: .env.local
    ↓
1Panel参考: 1panel/.env
```

## 🔧 环境变量定义

### 运行时环境变量 (env-unified.sh)

```bash
# 基础配置
NODE_ENV="production"
PORT="3000"

# 数据库配置 (绝对路径)
DATABASE_URL="sqlite:////opt/hypcup-event/data/app.db"
UPLOAD_DIR="/opt/hypcup-event/uploads"

# API配置
NEXT_PUBLIC_API_URL="http://localhost:8000/api"

# 应用安全配置
SECRET_KEY="ZaNTW3rPyFWnMhptPQEv8CmGtbJhMPVe8x2Q"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="123456"

# 前端构建配置
PACKAGE_MANAGER="pnpm"
CONTAINER_PACKAGE_URL="https://registry.npmmirror.com/"
```

## 🚀 使用方式

### 容器启动时

环境变量由 `env-unified.sh` 自动加载：

```bash
# 在 simple-run.sh 中
source /env-unified.sh
```

### 前端启动脚本

```bash
# 在 /start-frontend.sh 中
source /env-unified.sh
```

### 后端启动脚本

```bash
# 在 /start-backend.sh 中
source /env-unified.sh
```

## ⚠️ 注意事项

### 1. 修改环境变量

**只需要修改 `env-unified.sh` 一个文件！**

其他文件中的环境变量定义仅作为参考，不会影响运行时配置。

### 2. 数据库路径

使用绝对路径格式：`sqlite:////opt/hypcup-event/data/app.db`

注意是 **4个斜杠**，确保无论工作目录在哪里都使用正确的数据库文件。

### 3. API URL配置

- 容器内部通信：`http://localhost:8000/api`
- 如需外部访问，修改为：`http://your-server-ip:8000/api`

## 🔍 故障排除

### 环境变量不生效

1. 检查 `env-unified.sh` 是否正确挂载到容器
2. 确认启动脚本中有 `source /env-unified.sh`
3. 重新构建容器：`docker-compose up --build`

### 数据库连接问题

1. 确认 `DATABASE_URL` 使用绝对路径（4个斜杠）
2. 检查数据库文件是否存在：`/opt/hypcup-event/data/app.db`
3. 验证文件权限：容器内应该可读写

### API连接问题

1. 确认 `NEXT_PUBLIC_API_URL` 配置正确
2. 检查前后端端口映射
3. 验证网络连接

## 📝 配置变更记录

### v1.0 - 统一环境变量管理

- 创建 `env-unified.sh` 统一配置文件
- 修改启动脚本使用统一配置
- 清理重复的环境变量定义
- 修复数据库路径问题（使用绝对路径）

### 配置文件状态

- ✅ `env-unified.sh` - 主要配置文件
- ✅ `simple-run.sh` - 已更新使用统一配置
- ✅ `1panel/.env` - 已标记为参考配置
- ✅ `.env.production` - 已简化，避免冲突
- ✅ `.env.local` - 保持不变（本地开发用）

## 🎉 预期效果

统一环境变量管理后：

1. **消除配置冲突** - 只有一个地方定义运行时环境变量
2. **简化维护** - 修改配置只需要改一个文件
3. **避免路径问题** - 使用绝对路径确保数据库连接正确
4. **提高可靠性** - 减少因配置不一致导致的问题
