#!/usr/bin/env python3
"""
修正数据库中的奖项信息
将错误的"优秀奖"修正为"入围奖"
"""

import sys
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    import os
    os.chdir(str(backend_path))
    from app.models import user, work
    from app.core.database import SessionLocal
    from app.models.work import ArchitecturalWork
    from sqlalchemy import func
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class AwardFixer:
    def __init__(self):
        self.db = SessionLocal()
        
        # 正确的奖项分布（根据霍普杯竞赛规则）
        self.correct_awards = {
            "一等奖": 1,    # 1个一等奖
            "二等奖": 3,    # 3个二等奖  
            "三等奖": 8,    # 8个三等奖
            "入围奖": 40    # 40个入围奖（不是优秀奖）
        }
    
    def analyze_current_awards(self):
        """分析当前的奖项分布"""
        print("🔍 分析当前数据库中的奖项分布...")
        
        awards = self.db.query(
            ArchitecturalWork.award, 
            func.count(ArchitecturalWork.id)
        ).group_by(ArchitecturalWork.award).all()
        
        current_distribution = {}
        for award, count in awards:
            current_distribution[award] = count
            print(f"  {award}: {count} 个")
        
        return current_distribution
    
    def fix_awards(self):
        """修正奖项信息"""
        print("\n🔧 开始修正奖项信息...")
        
        # 将所有"优秀奖"改为"入围奖"
        works_to_fix = self.db.query(ArchitecturalWork).filter(
            ArchitecturalWork.award == "优秀奖"
        ).all()
        
        print(f"找到 {len(works_to_fix)} 个需要修正的作品（优秀奖 → 入围奖）")
        
        updated_count = 0
        for work in works_to_fix:
            old_award = work.award
            work.award = "入围奖"
            
            # 更新描述中的奖项信息
            if work.description:
                work.description = work.description.replace("获奖等级：优秀奖", "获奖等级：入围奖")
            
            # 更新标签中的奖项信息
            if work.tags:
                new_tags = []
                for tag in work.tags:
                    if tag == "优秀奖":
                        new_tags.append("入围奖")
                    else:
                        new_tags.append(tag)
                work.tags = new_tags
            
            print(f"  修正作品 ID={work.id}: {work.title[:30]}... ({old_award} → {work.award})")
            updated_count += 1
        
        # 提交更改
        try:
            self.db.commit()
            print(f"\n✅ 成功修正了 {updated_count} 个作品的奖项信息")
        except Exception as e:
            self.db.rollback()
            print(f"\n❌ 修正失败: {e}")
            return False
        
        return True
    
    def update_sorting_logic(self):
        """更新后端排序逻辑以包含入围奖"""
        print("\n📝 提醒：需要更新后端排序逻辑以包含'入围奖'")
        print("排序顺序应该是：一等奖 → 二等奖 → 三等奖 → 入围奖")
    
    def verify_fix(self):
        """验证修正结果"""
        print("\n✅ 验证修正结果...")
        
        awards = self.db.query(
            ArchitecturalWork.award, 
            func.count(ArchitecturalWork.id)
        ).group_by(ArchitecturalWork.award).all()
        
        print("修正后的奖项分布:")
        total_works = 0
        for award, count in awards:
            print(f"  {award}: {count} 个")
            total_works += count
        
        print(f"总计: {total_works} 个作品")
        
        # 检查是否符合预期
        expected_total = sum(self.correct_awards.values())
        if total_works == expected_total:
            print(f"✅ 作品总数正确 ({total_works}/{expected_total})")
        else:
            print(f"⚠️  作品总数不符合预期 ({total_works}/{expected_total})")
    
    def show_sample_works(self):
        """显示各奖项的示例作品"""
        print("\n📋 各奖项示例作品:")
        
        for award in ["一等奖", "二等奖", "三等奖", "入围奖"]:
            works = self.db.query(ArchitecturalWork).filter(
                ArchitecturalWork.award == award
            ).limit(3).all()
            
            if works:
                print(f"\n{award}:")
                for work in works:
                    print(f"  ID {work.id}: {work.title}")
    
    def close(self):
        """关闭数据库连接"""
        self.db.close()

def main():
    """主函数"""
    print("🏆 霍普杯奖项修正工具")
    print("=" * 50)
    
    fixer = AwardFixer()
    
    try:
        # 分析当前状态
        current_awards = fixer.analyze_current_awards()
        
        # 检查是否需要修正
        if "优秀奖" in current_awards:
            print(f"\n⚠️  发现问题：数据库中有 {current_awards['优秀奖']} 个'优秀奖'，应该是'入围奖'")
            
            # 询问是否执行修正
            print("\n是否要将所有'优秀奖'修正为'入围奖'？(y/N): ", end="")
            choice = input().strip().lower()
            
            if choice in ['y', 'yes']:
                # 执行修正
                if fixer.fix_awards():
                    # 验证结果
                    fixer.verify_fix()
                    fixer.show_sample_works()
                    fixer.update_sorting_logic()
                else:
                    print("修正失败，请检查错误信息")
            else:
                print("取消修正")
        else:
            print("\n✅ 数据库中没有发现'优秀奖'，奖项信息正确")
            fixer.verify_fix()
            fixer.show_sample_works()
    
    finally:
        fixer.close()

if __name__ == "__main__":
    main()
