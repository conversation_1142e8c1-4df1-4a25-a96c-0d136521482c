#!/usr/bin/env python3
"""
安装数据导入脚本所需的依赖
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def main():
    """安装所需依赖"""
    print("正在安装数据导入脚本所需的依赖...")
    
    packages = [
        "pandas",
        "openpyxl"
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功，可以运行导入脚本了")
    else:
        print("❌ 部分依赖安装失败，请手动安装")

if __name__ == "__main__":
    main()
