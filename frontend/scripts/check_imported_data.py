#!/usr/bin/env python3
"""
检查导入的霍普杯竞赛数据
"""

import sys
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    from app.core.database import SessionLocal
    from app.models import user, work
    from app.models.work import ArchitecturalWork
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def check_imported_data():
    """检查导入的数据"""
    db = SessionLocal()
    
    try:
        # 查询所有作品
        works = db.query(ArchitecturalWork).all()
        
        print(f"=== 霍普杯竞赛数据检查报告 ===")
        print(f"总作品数量: {len(works)}")
        print()
        
        # 按奖项统计
        award_stats = {}
        for work in works:
            award = work.award
            if award not in award_stats:
                award_stats[award] = 0
            award_stats[award] += 1
        
        print("奖项分布:")
        for award, count in award_stats.items():
            print(f"  {award}: {count} 个作品")
        print()
        
        # 检查图片数量
        total_images = 0
        works_with_images = 0
        for work in works:
            if work.images and len(work.images) > 0:
                works_with_images += 1
                total_images += len(work.images)
        
        print("图片统计:")
        print(f"  有图片的作品: {works_with_images} 个")
        print(f"  图片总数: {total_images} 张")
        print(f"  平均每作品图片数: {total_images/len(works):.1f} 张")
        print()
        
        # 显示前5个作品的详细信息
        print("前5个作品详情:")
        print("-" * 80)
        for i, work in enumerate(works[:5]):
            print(f"{i+1}. ID: {work.id}")
            print(f"   标题: {work.title}")
            print(f"   建筑师: {work.architect}")
            print(f"   奖项: {work.award}")
            print(f"   年份: {work.year}")
            print(f"   位置: {work.location}")
            print(f"   图片数量: {len(work.images) if work.images else 0}")
            print(f"   是否发布: {work.is_published}")
            print(f"   是否精选: {work.is_featured}")
            print(f"   标签: {work.tags}")
            print("-" * 80)
        
        # 检查数据完整性
        print("数据完整性检查:")
        missing_title = sum(1 for work in works if not work.title)
        missing_architect = sum(1 for work in works if not work.architect)
        missing_award = sum(1 for work in works if not work.award)
        missing_description = sum(1 for work in works if not work.description)
        
        print(f"  缺少标题: {missing_title} 个")
        print(f"  缺少建筑师: {missing_architect} 个")
        print(f"  缺少奖项: {missing_award} 个")
        print(f"  缺少描述: {missing_description} 个")
        
        if missing_title + missing_architect + missing_award + missing_description == 0:
            print("  ✅ 所有必要字段都已填写")
        else:
            print("  ⚠️ 存在缺失数据")
        
        print()
        print("=== 检查完成 ===")
        
    except Exception as e:
        print(f"检查数据失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_imported_data()
