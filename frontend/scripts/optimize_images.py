#!/usr/bin/env python3
"""
图片压缩优化脚本
使用FFmpeg对项目中的作品图片进行压缩优化，提升网站加载性能

功能：
1. 压缩现有图片，减小文件大小
2. 生成多种尺寸的缩略图
3. 转换为WebP格式（更好的压缩率）
4. 保留原图备份
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Tuple
import json
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

class ImageOptimizer:
    def __init__(self):
        self.ffmpeg_path = r"D:\Program Files\ffmpeg\ffmpeg.exe"
        self.project_root = Path(__file__).parent.parent
        
        # 图片目录
        self.frontend_images_dir = self.project_root / "public" / "uploads" / "works"
        self.backend_images_dir = self.project_root / "backend" / "uploads" / "works"
        
        # 备份目录
        self.backup_dir = self.project_root / "backup" / "original_images"
        
        # 优化后的图片目录
        self.optimized_dir = self.project_root / "public" / "uploads" / "works_optimized"
        
        # 确保目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.optimized_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查FFmpeg是否可用
        self.check_ffmpeg()
        
        # 压缩配置
        self.quality_settings = {
            'thumbnail': {'width': 300, 'quality': 75},    # 缩略图
            'medium': {'width': 800, 'quality': 80},       # 中等尺寸
            'large': {'width': 1200, 'quality': 85},       # 大尺寸
            'original': {'width': None, 'quality': 90}     # 原尺寸压缩
        }
    
    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ FFmpeg 检查成功")
                print(f"   路径: {self.ffmpeg_path}")
                version_line = result.stdout.split('\n')[0]
                print(f"   版本: {version_line}")
            else:
                raise Exception("FFmpeg 执行失败")
        except Exception as e:
            print(f"❌ FFmpeg 检查失败: {e}")
            print("请确保FFmpeg已正确安装在指定路径")
            sys.exit(1)
    
    def get_image_files(self) -> List[Path]:
        """获取所有需要优化的图片文件"""
        image_files = []
        
        # 支持的图片格式
        extensions = ['.jpg', '.jpeg', '.png', '.webp']
        
        # 从前端目录获取图片
        if self.frontend_images_dir.exists():
            for ext in extensions:
                image_files.extend(self.frontend_images_dir.glob(f'*{ext}'))
                image_files.extend(self.frontend_images_dir.glob(f'*{ext.upper()}'))
        
        print(f"找到 {len(image_files)} 个图片文件")
        return image_files
    
    def backup_original(self, image_path: Path) -> bool:
        """备份原始图片"""
        try:
            backup_path = self.backup_dir / image_path.name
            if not backup_path.exists():
                shutil.copy2(image_path, backup_path)
                return True
            return False
        except Exception as e:
            print(f"❌ 备份失败 {image_path.name}: {e}")
            return False
    
    def compress_image(self, input_path: Path, output_path: Path, 
                      width: int = None, quality: int = 85, format: str = 'webp') -> bool:
        """使用FFmpeg压缩图片"""
        try:
            cmd = [self.ffmpeg_path, '-i', str(input_path)]
            
            # 设置输出格式和质量
            if format == 'webp':
                cmd.extend(['-c:v', 'libwebp', '-quality', str(quality)])
            elif format == 'jpg':
                cmd.extend(['-c:v', 'mjpeg', '-q:v', str(100 - quality)])
            
            # 设置尺寸
            if width:
                cmd.extend(['-vf', f'scale={width}:-1'])
            
            # 覆盖输出文件
            cmd.extend(['-y', str(output_path)])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return True
            else:
                print(f"❌ 压缩失败 {input_path.name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 压缩异常 {input_path.name}: {e}")
            return False
    
    def optimize_single_image(self, image_path: Path) -> dict:
        """优化单个图片，生成多种尺寸"""
        result = {
            'original': str(image_path),
            'optimized': {},
            'backup': False,
            'success': False
        }
        
        try:
            # 备份原图
            result['backup'] = self.backup_original(image_path)
            
            # 获取原始文件信息
            original_size = image_path.stat().st_size
            base_name = image_path.stem
            
            print(f"🔄 处理图片: {image_path.name} ({original_size / 1024:.1f} KB)")
            
            # 生成不同尺寸的优化图片
            for size_name, settings in self.quality_settings.items():
                # WebP格式输出
                webp_name = f"{base_name}_{size_name}.webp"
                webp_path = self.optimized_dir / webp_name
                
                success = self.compress_image(
                    image_path, webp_path,
                    width=settings['width'],
                    quality=settings['quality'],
                    format='webp'
                )
                
                if success and webp_path.exists():
                    new_size = webp_path.stat().st_size
                    compression_ratio = (1 - new_size / original_size) * 100
                    
                    result['optimized'][size_name] = {
                        'path': str(webp_path),
                        'size': new_size,
                        'compression': f"{compression_ratio:.1f}%"
                    }
                    
                    print(f"  ✅ {size_name}: {new_size / 1024:.1f} KB (压缩 {compression_ratio:.1f}%)")
            
            result['success'] = len(result['optimized']) > 0
            
        except Exception as e:
            print(f"❌ 优化失败 {image_path.name}: {e}")
        
        return result
    
    def update_database_paths(self, optimization_results: List[dict]):
        """更新数据库中的图片路径"""
        try:
            # 添加后端路径到Python路径
            backend_path = self.project_root / "backend"
            sys.path.append(str(backend_path))
            
            from app.models import user, work
            from app.core.database import SessionLocal
            from app.models.work import ArchitecturalWork
            
            db = SessionLocal()
            
            # 创建路径映射
            path_mapping = {}
            for result in optimization_results:
                if result['success']:
                    original_name = Path(result['original']).name
                    base_name = Path(result['original']).stem
                    
                    # 使用medium尺寸作为默认显示图片
                    if 'medium' in result['optimized']:
                        optimized_path = result['optimized']['medium']['path']
                        relative_path = f"/uploads/works_optimized/{Path(optimized_path).name}"
                        path_mapping[f"/uploads/works/{original_name}"] = relative_path
            
            # 更新数据库中的图片路径
            works = db.query(ArchitecturalWork).all()
            updated_count = 0
            
            for work in works:
                updated = False
                
                # 更新封面图片
                if work.cover_image in path_mapping:
                    work.cover_image = path_mapping[work.cover_image]
                    updated = True
                
                # 更新图片列表
                if work.images:
                    new_images = []
                    for img_path in work.images:
                        if img_path in path_mapping:
                            new_images.append(path_mapping[img_path])
                        else:
                            new_images.append(img_path)
                    work.images = new_images
                    updated = True
                
                if updated:
                    updated_count += 1
            
            db.commit()
            db.close()
            
            print(f"✅ 数据库更新完成，更新了 {updated_count} 个作品的图片路径")
            
        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
    
    def optimize_all_images(self, max_workers: int = 4):
        """批量优化所有图片"""
        image_files = self.get_image_files()
        
        if not image_files:
            print("没有找到需要优化的图片文件")
            return
        
        print(f"\n开始优化 {len(image_files)} 个图片文件...")
        print(f"使用 {max_workers} 个并发线程")
        print("-" * 60)
        
        start_time = time.time()
        optimization_results = []
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_image = {
                executor.submit(self.optimize_single_image, img_path): img_path 
                for img_path in image_files
            }
            
            # 收集结果
            for future in as_completed(future_to_image):
                result = future.result()
                optimization_results.append(result)
        
        # 统计结果
        successful = sum(1 for r in optimization_results if r['success'])
        failed = len(optimization_results) - successful
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("-" * 60)
        print(f"✅ 优化完成!")
        print(f"   成功: {successful} 个")
        print(f"   失败: {failed} 个")
        print(f"   耗时: {duration:.1f} 秒")
        
        # 更新数据库路径
        if successful > 0:
            print("\n🔄 更新数据库中的图片路径...")
            self.update_database_paths(optimization_results)
        
        return optimization_results
    
    def generate_report(self, results: List[dict]):
        """生成优化报告"""
        report_path = self.project_root / "scripts" / "optimization_report.json"
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_images': len(results),
            'successful': sum(1 for r in results if r['success']),
            'failed': sum(1 for r in results if not r['success']),
            'details': results
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 优化报告已保存: {report_path}")

def main():
    """主函数"""
    print("🖼️  图片压缩优化工具")
    print("=" * 50)
    
    optimizer = ImageOptimizer()
    
    # 执行优化
    results = optimizer.optimize_all_images(max_workers=4)
    
    if results:
        # 生成报告
        optimizer.generate_report(results)
    
    print("\n🎉 图片优化完成！")

if __name__ == "__main__":
    main()
