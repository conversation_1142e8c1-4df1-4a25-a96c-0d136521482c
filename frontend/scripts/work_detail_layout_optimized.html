<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品详情页布局优化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .layout-grid {
            display: grid;
            grid-template-columns: 3fr 1fr;
            gap: 24px;
            margin-bottom: 40px;
        }
        .image-section {
            position: relative;
        }
        .main-image {
            width: 100%;
            aspect-ratio: 3/2;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
        }
        .main-image:hover {
            transform: scale(1.02);
        }
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            cursor: pointer;
            transition: background 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        .nav-button:hover {
            background: rgba(0,0,0,0.7);
        }
        .nav-button.prev {
            left: 16px;
        }
        .nav-button.next {
            right: 16px;
        }
        .dots-nav {
            position: absolute;
            bottom: 24px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 12px;
        }
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.2s ease;
        }
        .dot.active {
            background: white;
        }
        .info-section {
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            height: fit-content;
        }
        .award-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            background: #dc3545;
            color: white;
            border-radius: 16px;
            font-weight: bold;
            margin-bottom: 16px;
            font-size: 14px;
        }
        .work-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            line-height: 1.3;
        }
        .architect {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 16px;
        }
        .stat-card {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-icon {
            color: #dc3545;
            margin-bottom: 4px;
            font-size: 12px;
        }
        .stat-icon.views { color: #007bff; }
        .stat-number {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        .stat-label {
            font-size: 10px;
            color: #666;
        }
        .detail-card {
            background: white;
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .detail-icon {
            color: #6c757d;
            font-size: 16px;
        }
        .detail-content .label {
            font-size: 10px;
            color: #666;
            margin-bottom: 1px;
        }
        .detail-content .value {
            font-weight: 500;
            font-size: 14px;
        }
        .tags-section {
            margin-bottom: 16px;
        }
        .tags-title {
            font-size: 10px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        .tag {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            color: #495057;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 16px;
        }
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
        }
        .btn-primary {
            background: #dc3545;
            color: white;
        }
        .btn-primary:hover {
            background: #c82333;
        }
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .btn-outline:hover {
            background: #f8f9fa;
        }
        .description-section {
            grid-column: 1 / -1;
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            margin-top: 32px;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .description-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            color: #495057;
            line-height: 1.7;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .comparison-item.before {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .comparison-item.after {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .comparison-title {
            font-weight: bold;
            margin-bottom: 12px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 4px 0;
            position: relative;
            padding-left: 20px;
        }
        .feature-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #007bff;
            font-weight: bold;
        }
        .highlight {
            background: #e3f2fd;
            padding: 16px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        @media (max-width: 768px) {
            .layout-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 作品详情页布局优化</h1>
            <p>删除缩略图，放大主图片，缩小右侧信息栏</p>
        </div>

        <div class="comparison">
            <div class="comparison-item before">
                <div class="comparison-title">🔄 调整前</div>
                <ul class="feature-list">
                    <li>左侧图片占2/3宽度 (66%)</li>
                    <li>右侧信息占1/3宽度 (33%)</li>
                    <li>图片比例 4:3</li>
                    <li>包含缩略图网格</li>
                    <li>信息栏较宽，内容分散</li>
                </ul>
            </div>
            <div class="comparison-item after">
                <div class="comparison-title">✅ 调整后</div>
                <ul class="feature-list">
                    <li>左侧图片占3/4宽度 (75%)</li>
                    <li>右侧信息占1/4宽度 (25%)</li>
                    <li>图片比例 3:2 (更宽)</li>
                    <li>删除缩略图网格</li>
                    <li>信息栏紧凑，内容集中</li>
                </ul>
            </div>
        </div>

        <h2>🖼️ 优化后的布局演示</h2>
        
        <div class="layout-grid">
            <!-- 左侧图片区域 (75%宽度) -->
            <div class="image-section">
                <div class="main-image">
                    主图片区域 (3:2 比例)
                    <br>更大的显示空间
                    
                    <!-- 导航按钮 -->
                    <button class="nav-button prev">‹</button>
                    <button class="nav-button next">›</button>
                    
                    <!-- 圆点导航 -->
                    <div class="dots-nav">
                        <div class="dot active"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧信息区域 (25%宽度) -->
            <div class="info-section">
                <div class="award-badge">
                    🏆 三等奖
                </div>

                <div class="work-title">公共天井计划——进化的竹筒屋社区</div>
                <div class="architect">文凯、范宇飞、井松青、钟启恩</div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">❤️</div>
                        <div class="stat-number">89</div>
                        <div class="stat-label">点赞</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon views">👁️</div>
                        <div class="stat-number">705</div>
                        <div class="stat-label">浏览</div>
                    </div>
                </div>

                <div class="detail-card">
                    <div class="detail-icon">📅</div>
                    <div class="detail-content">
                        <div class="label">竞赛年份</div>
                        <div class="value">2024年</div>
                    </div>
                </div>

                <div class="detail-card">
                    <div class="detail-icon">📍</div>
                    <div class="detail-content">
                        <div class="label">学校/机构</div>
                        <div class="value">华南理工大学</div>
                    </div>
                </div>

                <div class="tags-section">
                    <div class="tags-title">标签</div>
                    <div class="tags">
                        <span class="tag">UIA-霍普杯</span>
                        <span class="tag">2024</span>
                        <span class="tag">国际竞赛</span>
                        <span class="tag">三等奖</span>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary">❤️ 点赞</button>
                    <button class="btn btn-outline">🔖 收藏</button>
                </div>
            </div>
        </div>

        <!-- 作品简介区域 -->
        <div class="description-section">
            <div class="section-title">作品简介</div>
            <div class="description-content">
                本作品参加了UIA-霍普杯2024国际大学生建筑设计竞赛，以"公共天井计划——进化的竹筒屋社区"为主题，探索了传统建筑形式在现代城市环境中的创新应用。设计团队通过对竹筒屋这一传统建筑类型的深入研究，提出了适应当代城市生活需求的进化方案。
            </div>
        </div>

        <div class="highlight">
            <h3>🎯 主要优化点</h3>
            <ul class="feature-list">
                <li><strong>图片区域扩大</strong>：从66%增加到75%，图片显示更突出</li>
                <li><strong>图片比例调整</strong>：从4:3改为3:2，更适合横向构图</li>
                <li><strong>删除缩略图</strong>：简化界面，减少视觉干扰</li>
                <li><strong>信息栏紧凑化</strong>：从33%缩小到25%，内容更集中</li>
                <li><strong>导航按钮增大</strong>：提升操作体验</li>
                <li><strong>圆点导航优化</strong>：更大更清晰的指示器</li>
            </ul>
        </div>

        <h3>📐 布局规格</h3>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>网格布局：</strong> <code>grid-template-columns: 3fr 1fr</code></p>
            <p><strong>图片比例：</strong> <code>aspect-ratio: 3/2</code></p>
            <p><strong>最大宽度：</strong> <code>max-w-7xl</code> (1280px)</p>
            <p><strong>间距：</strong> <code>gap-6</code> (24px)</p>
        </div>

        <h3>📱 响应式适配</h3>
        <ul class="feature-list">
            <li><strong>桌面端</strong>：3:1 分栏布局，图片占主导</li>
            <li><strong>平板端</strong>：保持分栏，调整间距</li>
            <li><strong>移动端</strong>：单列布局，图片在上，信息在下</li>
        </ul>

        <div class="highlight">
            <h3>🌐 访问地址</h3>
            <p><strong>优化后的作品详情页：</strong> <a href="http://localhost:3000/work/65" target="_blank">http://localhost:3000/work/65</a></p>
            <p>现在图片显示更加突出，信息展示更加紧凑，整体布局更加合理！</p>
        </div>
    </div>

    <script>
        console.log('🎨 作品详情页布局优化完成');
        console.log('✅ 主要改进：');
        console.log('  - 图片区域从66%扩大到75%');
        console.log('  - 信息栏从33%缩小到25%');
        console.log('  - 删除缩略图网格');
        console.log('  - 图片比例从4:3改为3:2');
        console.log('  - 导航元素优化');
        
        // 简单的交互效果
        document.querySelectorAll('.dot').forEach((dot, index) => {
            dot.addEventListener('click', function() {
                document.querySelectorAll('.dot').forEach(d => d.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
