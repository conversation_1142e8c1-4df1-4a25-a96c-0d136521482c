#!/usr/bin/env python3
"""
更新数据库中的图片路径，使用优化后的压缩图片
"""

import sys
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    from app.core.database import SessionLocal
    from app.models import user, work
    from app.models.work import ArchitecturalWork
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class ImagePathUpdater:
    def __init__(self):
        # 确保使用后端目录的数据库
        import os
        os.chdir(str(backend_path))
        self.db = SessionLocal()
        self.project_root = current_dir
        self.optimized_dir = self.project_root / "public" / "uploads" / "works_optimized"
        
    def get_optimized_image_path(self, original_path: str, size: str = 'medium') -> str:
        """
        将原始图片路径转换为优化后的图片路径
        
        Args:
            original_path: 原始图片路径，如 "/uploads/works/146_1.jpg"
            size: 图片尺寸类型 (thumbnail, medium, large, original)
        
        Returns:
            优化后的图片路径，如 "/uploads/works_optimized/146_1_medium.webp"
        """
        if not original_path or not original_path.startswith('/uploads/works/'):
            return original_path
            
        # 提取文件名
        filename = original_path.split('/')[-1]
        base_name = filename.replace('.jpg', '').replace('.jpeg', '').replace('.png', '')
        
        # 生成优化后的路径
        optimized_filename = f"{base_name}_{size}.webp"
        optimized_path = f"/uploads/works_optimized/{optimized_filename}"
        
        # 检查文件是否存在
        full_path = self.optimized_dir / optimized_filename
        if full_path.exists():
            return optimized_path
        else:
            print(f"⚠️  优化图片不存在: {optimized_filename}")
            return original_path
    
    def update_work_images(self, work: ArchitecturalWork) -> bool:
        """更新单个作品的图片路径"""
        updated = False
        
        # 更新封面图片
        if work.cover_image:
            new_cover = self.get_optimized_image_path(work.cover_image, 'medium')
            if new_cover != work.cover_image:
                print(f"更新封面图片: {work.cover_image} -> {new_cover}")
                work.cover_image = new_cover
                updated = True
        
        # 更新图片列表
        if work.images:
            new_images = []
            for img_path in work.images:
                # 为详情页使用large尺寸
                new_path = self.get_optimized_image_path(img_path, 'large')
                new_images.append(new_path)
                if new_path != img_path:
                    print(f"更新图片: {img_path} -> {new_path}")
                    updated = True
            
            work.images = new_images
        
        return updated
    
    def update_all_images(self):
        """更新所有作品的图片路径"""
        print("🔄 开始更新数据库中的图片路径...")
        
        try:
            # 获取所有作品
            works = self.db.query(ArchitecturalWork).all()
            print(f"找到 {len(works)} 个作品")
            
            updated_count = 0
            
            for work in works:
                print(f"\n处理作品 ID={work.id}: {work.title}")
                
                if self.update_work_images(work):
                    updated_count += 1
            
            # 提交更改
            self.db.commit()
            print(f"\n✅ 更新完成！共更新了 {updated_count} 个作品的图片路径")
            
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            self.db.rollback()
        finally:
            self.db.close()
    
    def check_optimized_images(self):
        """检查优化图片的可用性"""
        print("🔍 检查优化图片文件...")
        
        if not self.optimized_dir.exists():
            print(f"❌ 优化图片目录不存在: {self.optimized_dir}")
            return False
        
        # 统计优化图片文件
        webp_files = list(self.optimized_dir.glob("*.webp"))
        print(f"找到 {len(webp_files)} 个优化图片文件")
        
        # 按尺寸分类统计
        size_stats = {
            'thumbnail': 0,
            'medium': 0,
            'large': 0,
            'original': 0
        }
        
        for file in webp_files:
            for size in size_stats.keys():
                if f"_{size}.webp" in file.name:
                    size_stats[size] += 1
                    break
        
        print("按尺寸分类:")
        for size, count in size_stats.items():
            print(f"  {size}: {count} 个文件")
        
        return len(webp_files) > 0
    
    def generate_report(self):
        """生成图片路径更新报告"""
        print("\n📊 图片路径更新报告")
        print("=" * 50)
        
        # 检查优化图片
        has_optimized = self.check_optimized_images()
        
        if not has_optimized:
            print("❌ 没有找到优化图片文件，请先运行图片压缩脚本")
            return
        
        # 检查数据库中的图片路径
        works = self.db.query(ArchitecturalWork).all()
        
        original_paths = 0
        optimized_paths = 0
        
        for work in works:
            # 检查封面图片
            if work.cover_image:
                if 'works_optimized' in work.cover_image:
                    optimized_paths += 1
                else:
                    original_paths += 1
            
            # 检查图片列表
            if work.images:
                for img_path in work.images:
                    if 'works_optimized' in img_path:
                        optimized_paths += 1
                    else:
                        original_paths += 1
        
        print(f"数据库图片路径统计:")
        print(f"  原始路径: {original_paths} 个")
        print(f"  优化路径: {optimized_paths} 个")
        
        if original_paths > 0:
            print(f"\n建议运行更新脚本将 {original_paths} 个原始路径更新为优化路径")
        else:
            print(f"\n✅ 所有图片路径都已使用优化版本")

def main():
    """主函数"""
    updater = ImagePathUpdater()
    
    print("🖼️  图片路径更新工具")
    print("=" * 40)
    
    # 生成报告
    updater.generate_report()
    
    # 询问是否执行更新
    print("\n是否要更新数据库中的图片路径？(y/N): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes']:
        updater.update_all_images()
    else:
        print("取消更新")

if __name__ == "__main__":
    main()
