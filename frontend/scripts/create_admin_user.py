#!/usr/bin/env python3
"""
创建管理员用户
"""

import sys
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    import os
    os.chdir(str(backend_path))
    from app.core.database import SessionLocal
    from sqlalchemy import text
    from passlib.context import CryptContext
    from datetime import datetime
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class AdminUserCreator:
    def __init__(self):
        self.db = SessionLocal()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # 管理员账号信息
        self.admin_accounts = [
            {
                "email": "<EMAIL>",
                "username": "admin",
                "password": "123456",
                "is_admin": True
            },
            {
                "email": "<EMAIL>", 
                "username": "admin2",
                "password": "admin123",
                "is_admin": True
            }
        ]
    
    def hash_password(self, password: str) -> str:
        """加密密码"""
        return self.pwd_context.hash(password)
    
    def check_existing_users(self):
        """检查现有用户"""
        try:
            result = self.db.execute(text("SELECT email, username, is_admin FROM users"))
            users = result.fetchall()
            
            if users:
                print("现有用户:")
                for user in users:
                    print(f"  邮箱: {user[0]}, 用户名: {user[1]}, 管理员: {user[2]}")
                return users
            else:
                print("数据库中没有用户")
                return []
        except Exception as e:
            print(f"检查用户失败: {e}")
            return []
    
    def create_admin_user(self, account_info):
        """创建管理员用户"""
        try:
            # 检查用户是否已存在
            result = self.db.execute(
                text("SELECT id FROM users WHERE email = :email"),
                {"email": account_info["email"]}
            )
            existing_user = result.fetchone()
            
            if existing_user:
                print(f"用户 {account_info['email']} 已存在，跳过创建")
                return False
            
            # 加密密码
            hashed_password = self.hash_password(account_info["password"])
            
            # 插入新用户
            self.db.execute(text("""
                INSERT INTO users (email, username, hashed_password, is_active, is_admin, created_at)
                VALUES (:email, :username, :hashed_password, :is_active, :is_admin, :created_at)
            """), {
                "email": account_info["email"],
                "username": account_info["username"],
                "hashed_password": hashed_password,
                "is_active": True,
                "is_admin": account_info["is_admin"],
                "created_at": datetime.utcnow()
            })
            
            self.db.commit()
            
            print(f"✅ 成功创建管理员用户:")
            print(f"   邮箱: {account_info['email']}")
            print(f"   用户名: {account_info['username']}")
            print(f"   密码: {account_info['password']}")
            print(f"   管理员权限: {account_info['is_admin']}")
            
            return True
            
        except Exception as e:
            self.db.rollback()
            print(f"❌ 创建用户失败: {e}")
            return False
    
    def create_all_admin_users(self):
        """创建所有管理员用户"""
        print("🔧 开始创建管理员用户...")
        print("=" * 50)
        
        # 检查现有用户
        existing_users = self.check_existing_users()
        print()
        
        created_count = 0
        
        for account in self.admin_accounts:
            print(f"创建管理员账号: {account['email']}")
            if self.create_admin_user(account):
                created_count += 1
            print()
        
        print(f"总计创建了 {created_count} 个管理员账号")
        
        # 验证创建结果
        print("\n验证创建结果:")
        self.check_existing_users()
    
    def test_login(self, email, password):
        """测试登录"""
        try:
            result = self.db.execute(
                text("SELECT hashed_password, is_admin FROM users WHERE email = :email"),
                {"email": email}
            )
            user = result.fetchone()
            
            if not user:
                print(f"❌ 用户 {email} 不存在")
                return False
            
            # 验证密码
            if self.pwd_context.verify(password, user[0]):
                print(f"✅ 用户 {email} 登录成功")
                print(f"   管理员权限: {user[1]}")
                return True
            else:
                print(f"❌ 用户 {email} 密码错误")
                return False
                
        except Exception as e:
            print(f"❌ 登录测试失败: {e}")
            return False
    
    def test_all_accounts(self):
        """测试所有账号登录"""
        print("\n🧪 测试所有管理员账号登录...")
        print("=" * 50)
        
        for account in self.admin_accounts:
            print(f"测试账号: {account['email']}")
            self.test_login(account['email'], account['password'])
            print()
    
    def close(self):
        """关闭数据库连接"""
        self.db.close()

def main():
    """主函数"""
    print("🔐 管理员用户创建工具")
    print("=" * 50)
    
    creator = AdminUserCreator()
    
    try:
        # 创建管理员用户
        creator.create_all_admin_users()
        
        # 测试登录
        creator.test_all_accounts()
        
        print("\n📋 管理员账号信息:")
        print("=" * 30)
        for account in creator.admin_accounts:
            print(f"邮箱: {account['email']}")
            print(f"密码: {account['password']}")
            print(f"用户名: {account['username']}")
            print("-" * 30)
        
        print("\n✅ 管理员用户创建完成！")
        print("现在可以使用以上账号登录管理后台")
        
    finally:
        creator.close()

if __name__ == "__main__":
    main()
