#!/usr/bin/env python3
"""
测试密码修改API
"""

import requests
import json

def test_password_change_api():
    """测试密码修改API"""
    print("🔐 测试密码修改API")
    print("=" * 50)
    
    # 测试账号
    test_email = "<EMAIL>"
    current_password = "123456"
    new_password = "gogogo000"
    
    # 1. 先登录获取token
    print("1. 登录获取token...")
    login_url = "http://localhost:8000/api/auth/login"
    
    login_data = {
        "username": test_email,
        "password": current_password
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"✅ 登录成功，获取到token: {access_token[:30]}...")
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 2. 测试密码修改API
    print("\n2. 测试密码修改...")
    change_password_url = "http://localhost:8000/api/user/change-password"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # 测试正确的字段名格式
    password_data = {
        "current_password": current_password,
        "new_password": new_password
    }
    
    try:
        response = requests.post(
            change_password_url, 
            headers=headers,
            json=password_data
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 密码修改成功！")
            
            # 3. 验证新密码是否生效
            print("\n3. 验证新密码...")
            login_data_new = {
                "username": test_email,
                "password": new_password
            }
            
            response = requests.post(login_url, data=login_data_new)
            
            if response.status_code == 200:
                print("✅ 新密码登录成功！")
                
                # 4. 恢复原密码
                print("\n4. 恢复原密码...")
                new_token = response.json().get("access_token")
                headers["Authorization"] = f"Bearer {new_token}"
                
                restore_data = {
                    "current_password": new_password,
                    "new_password": current_password
                }
                
                response = requests.post(
                    change_password_url,
                    headers=headers,
                    json=restore_data
                )
                
                if response.status_code == 200:
                    print("✅ 密码已恢复为原密码")
                else:
                    print(f"⚠️ 密码恢复失败: {response.status_code} - {response.text}")
            else:
                print(f"❌ 新密码登录失败: {response.status_code} - {response.text}")
        else:
            print(f"❌ 密码修改失败: {response.status_code}")
            
            # 解析错误信息
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误文本: {response.text}")
                
    except Exception as e:
        print(f"❌ 密码修改请求失败: {e}")

def test_frontend_api():
    """测试前端API代理"""
    print("\n🌐 测试前端API代理")
    print("=" * 50)
    
    # 测试账号
    test_email = "<EMAIL>"
    current_password = "123456"
    new_password = "gogogo000"
    
    # 1. 通过前端API登录
    print("1. 通过前端API登录...")
    login_url = "http://localhost:3000/api/auth/login"
    
    login_data = {
        "username": test_email,
        "password": current_password
    }
    
    try:
        # 使用FormData格式
        response = requests.post(login_url, data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"✅ 前端API登录成功，获取到token: {access_token[:30]}...")
        else:
            print(f"❌ 前端API登录失败: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 前端API登录请求失败: {e}")
        return
    
    # 2. 通过前端API测试密码修改
    print("\n2. 通过前端API测试密码修改...")
    change_password_url = "http://localhost:3000/api/user/change-password"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # 使用前端格式的字段名
    password_data = {
        "currentPassword": current_password,
        "newPassword": new_password
    }
    
    try:
        response = requests.post(
            change_password_url, 
            headers=headers,
            json=password_data
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 前端API密码修改成功！")
        else:
            print(f"❌ 前端API密码修改失败")
            
            # 解析错误信息
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误文本: {response.text}")
                
    except Exception as e:
        print(f"❌ 前端API密码修改请求失败: {e}")

def main():
    """主函数"""
    print("🧪 密码修改API测试")
    print("=" * 60)
    
    # 测试后端API
    test_password_change_api()
    
    # 测试前端API代理
    test_frontend_api()
    
    print("\n📋 测试总结")
    print("=" * 30)
    print("如果测试成功，说明密码修改功能正常工作")
    print("如果测试失败，请检查:")
    print("1. 后端服务是否正常运行")
    print("2. 前端服务是否正常运行") 
    print("3. API字段名是否匹配")
    print("4. 认证token是否有效")

if __name__ == "__main__":
    main()
