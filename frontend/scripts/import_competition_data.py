#!/usr/bin/env python3
"""
霍普杯竞赛数据导入脚本
从Excel文件读取竞赛数据并导入到数据库，同时复制图片文件

使用前请安装依赖：
pip install pandas openpyxl

运行方式：
cd F:/Project/event-listing-v6
python scripts/import_competition_data.py
"""

import os
import sys
import shutil
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    from app.core.database import SessionLocal
    # 先导入所有模型以避免循环依赖
    from app.models import user, work
    from app.models.work import ArchitecturalWork
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class CompetitionDataImporter:
    def __init__(self):
        self.excel_path = r"E:\UED\霍普杯\UIA-霍普杯2024国际大学生建筑设计竞获奖名单.xlsx12.10.xlsx"
        self.images_source_dir = r"E:\UED\霍普杯\2024压缩图纸"

        # 项目目录结构
        self.project_root = current_dir
        self.images_target_dir = self.project_root / "public" / "uploads" / "works"
        self.backend_uploads_dir = self.project_root / "backend" / "uploads" / "works"

        # 确保目标目录存在
        self.images_target_dir.mkdir(parents=True, exist_ok=True)
        self.backend_uploads_dir.mkdir(parents=True, exist_ok=True)

        # 初始化数据库连接
        try:
            self.db = SessionLocal()
            print("✅ 数据库连接成功")

            # 创建数据库表
            self.create_tables()
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)

        # 奖项映射
        self.award_mapping = {
            "一等奖 1st Prize": "一等奖",
            "二等奖 2nd Prize": "二等奖",
            "三等奖 3rd Prize": "三等奖",
            "优秀奖 Honorable Mention": "优秀奖"
        }

    def create_tables(self):
        """创建数据库表"""
        try:
            from app.core.database import engine
            from app.models.user import Base as UserBase
            from app.models.work import Base as WorkBase

            # 创建所有表
            UserBase.metadata.create_all(bind=engine)
            WorkBase.metadata.create_all(bind=engine)
            print("✅ 数据库表创建成功")
        except Exception as e:
            print(f"❌ 创建数据库表失败: {e}")

    def read_excel_data(self) -> List[Dict[str, Any]]:
        """读取Excel文件数据"""
        print(f"正在读取Excel文件: {self.excel_path}")
        
        try:
            # 读取Excel文件，跳过标题行
            df = pd.read_excel(self.excel_path, header=None)
            
            works_data = []
            current_award = None
            
            for index, row in df.iterrows():
                # 检查是否是奖项标题行
                if pd.notna(row[0]) and any(award in str(row[0]) for award in self.award_mapping.keys()):
                    for award_key, award_value in self.award_mapping.items():
                        if award_key in str(row[0]):
                            current_award = award_value
                            print(f"处理奖项: {current_award}")
                            break
                    continue
                
                # 检查是否是数据行（有ID、小组成员、作品名等）
                if (pd.notna(row[1]) and pd.notna(row[2]) and pd.notna(row[3]) and 
                    current_award and str(row[1]).isdigit()):
                    
                    work_data = {
                        'id': int(row[1]),
                        'architect': str(row[2]).strip(),
                        'title': str(row[3]).strip(),
                        'advisor': str(row[4]).strip() if pd.notna(row[4]) else "",
                        'location': str(row[5]).strip() if pd.notna(row[5]) else "",
                        'award': current_award,
                        'year': 2024
                    }
                    
                    works_data.append(work_data)
                    print(f"添加作品: ID={work_data['id']}, 标题={work_data['title'][:20]}...")
            
            print(f"共读取到 {len(works_data)} 个作品")
            return works_data
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return []
    
    def copy_images(self, work_id: int) -> List[str]:
        """复制作品图片到项目目录"""
        image_files = []

        for i in range(1, 4):  # 每个作品3张图片
            # 根据您提供的格式：ID=146 对应 14601.jpg, 14602.jpg, 14603.jpg
            source_filename = f"{work_id:03d}{i:02d}.jpg"  # 格式：14601.jpg (3位ID + 2位序号)
            source_path = Path(self.images_source_dir) / source_filename

            if source_path.exists():
                # 目标文件名：work_id_image_number.jpg
                target_filename = f"{work_id}_{i}.jpg"

                # 复制到前端public目录
                frontend_target = self.images_target_dir / target_filename
                # 复制到后端uploads目录
                backend_target = self.backend_uploads_dir / target_filename

                try:
                    # 复制到两个位置
                    shutil.copy2(source_path, frontend_target)
                    shutil.copy2(source_path, backend_target)

                    # 返回相对于uploads的路径（后端API使用）
                    relative_path = f"/uploads/works/{target_filename}"
                    image_files.append(relative_path)
                    print(f"✅ 复制图片: {source_filename} -> {target_filename}")
                except Exception as e:
                    print(f"❌ 复制图片失败 {source_filename}: {e}")
            else:
                print(f"⚠️  图片文件不存在: {source_filename}")

        return image_files
    
    def generate_description(self, work_data: Dict[str, Any]) -> str:
        """生成作品描述"""
        description_parts = []
        
        # 基本信息
        description_parts.append(f"作品名称：{work_data['title']}")
        description_parts.append(f"建筑师：{work_data['architect']}")
        
        if work_data['advisor']:
            description_parts.append(f"指导老师：{work_data['advisor']}")
        
        if work_data['location']:
            description_parts.append(f"学校：{work_data['location']}")
        
        description_parts.append(f"获奖等级：{work_data['award']}")
        description_parts.append(f"竞赛年份：{work_data['year']}")
        
        # 添加竞赛信息
        description_parts.append("\n本作品参加了UIA-霍普杯2024国际大学生建筑设计竞赛，展现了优秀的建筑设计理念和创新思维。")
        
        return "\n".join(description_parts)
    
    def insert_work_to_db(self, work_data: Dict[str, Any], images: List[str]) -> bool:
        """将作品数据插入数据库"""
        try:
            # 检查是否已存在相同ID的作品
            existing_work = self.db.query(ArchitecturalWork).filter(
                ArchitecturalWork.id == work_data['id']
            ).first()

            if existing_work:
                print(f"⚠️  作品ID {work_data['id']} 已存在，跳过")
                return False

            # 生成描述
            description = self.generate_description(work_data)

            # 创建作品记录
            db_work = ArchitecturalWork(
                id=work_data['id'],
                title=work_data['title'],
                architect=work_data['architect'],
                year=work_data['year'],
                award=work_data['award'],
                description=description,
                location=work_data['location'],
                cover_image=images[0] if images else "/placeholder.jpg",
                images=images,
                tags=["UIA-霍普杯", "2024", "国际竞赛", work_data['award']],
                is_published=True,
                is_featured=work_data['award'] in ["一等奖", "二等奖"],
                likes_count=0,
                views_count=0,
                creator_id=None  # 竞赛作品没有特定创建者
            )

            self.db.add(db_work)
            self.db.commit()

            print(f"✅ 成功插入作品: {work_data['title']}")
            return True

        except Exception as e:
            print(f"❌ 插入作品失败 {work_data['title']}: {e}")
            print(f"   错误详情: {str(e)}")
            self.db.rollback()
            return False
    
    def import_data(self):
        """执行完整的数据导入流程"""
        print("开始导入霍普杯竞赛数据...")
        
        # 1. 读取Excel数据
        works_data = self.read_excel_data()
        if not works_data:
            print("没有读取到有效数据，退出")
            return
        
        # 2. 处理每个作品
        success_count = 0
        failed_count = 0
        
        for work_data in works_data:
            print(f"\n处理作品 ID={work_data['id']}: {work_data['title']}")
            
            # 复制图片
            images = self.copy_images(work_data['id'])
            
            if not images:
                print(f"⚠️  作品 {work_data['id']} 没有找到图片文件")
            
            # 插入数据库
            if self.insert_work_to_db(work_data, images):
                success_count += 1
            else:
                failed_count += 1
        
        # 3. 输出统计结果
        print(f"\n=== 导入完成 ===")
        print(f"成功导入: {success_count} 个作品")
        print(f"失败/跳过: {failed_count} 个作品")
        print(f"总计处理: {len(works_data)} 个作品")
        
        self.db.close()

def main():
    """主函数"""
    importer = CompetitionDataImporter()
    importer.import_data()

if __name__ == "__main__":
    main()
