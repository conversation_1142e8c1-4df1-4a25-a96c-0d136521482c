#!/usr/bin/env python3
"""
性能测试脚本
比较优化前后的图片大小和加载性能
"""

import os
import time
import requests
from pathlib import Path
from typing import Dict, List, Tuple

class PerformanceTester:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.original_dir = self.project_root / "public" / "uploads" / "works"
        self.optimized_dir = self.project_root / "public" / "uploads" / "works_optimized"
        
    def get_file_size(self, file_path: Path) -> int:
        """获取文件大小（字节）"""
        try:
            return file_path.stat().st_size
        except:
            return 0
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / 1024 / 1024:.1f} MB"
    
    def analyze_compression_results(self) -> Dict:
        """分析压缩结果"""
        results = {
            'total_original_size': 0,
            'total_optimized_size': 0,
            'files_analyzed': 0,
            'compression_details': [],
            'size_breakdown': {
                'thumbnail': {'original': 0, 'optimized': 0, 'count': 0},
                'medium': {'original': 0, 'optimized': 0, 'count': 0},
                'large': {'original': 0, 'optimized': 0, 'count': 0},
                'original': {'original': 0, 'optimized': 0, 'count': 0}
            }
        }
        
        # 获取所有原始图片
        if not self.original_dir.exists():
            print(f"❌ 原始图片目录不存在: {self.original_dir}")
            return results
            
        original_files = list(self.original_dir.glob("*.jpg"))
        print(f"找到 {len(original_files)} 个原始图片文件")
        
        for original_file in original_files:
            base_name = original_file.stem
            original_size = self.get_file_size(original_file)
            
            if original_size == 0:
                continue
                
            results['total_original_size'] += original_size
            results['files_analyzed'] += 1
            
            file_detail = {
                'filename': original_file.name,
                'original_size': original_size,
                'optimized_sizes': {},
                'total_optimized_size': 0
            }
            
            # 检查各种尺寸的优化版本
            for size_type in ['thumbnail', 'medium', 'large', 'original']:
                optimized_file = self.optimized_dir / f"{base_name}_{size_type}.webp"
                
                if optimized_file.exists():
                    optimized_size = self.get_file_size(optimized_file)
                    file_detail['optimized_sizes'][size_type] = optimized_size
                    file_detail['total_optimized_size'] += optimized_size
                    
                    # 更新统计
                    results['size_breakdown'][size_type]['original'] += original_size
                    results['size_breakdown'][size_type]['optimized'] += optimized_size
                    results['size_breakdown'][size_type]['count'] += 1
            
            results['total_optimized_size'] += file_detail['total_optimized_size']
            results['compression_details'].append(file_detail)
        
        return results
    
    def test_loading_performance(self, base_url: str = "http://localhost:3000") -> Dict:
        """测试加载性能"""
        print(f"\n🔄 测试加载性能...")
        
        # 测试图片列表
        test_images = [
            "/uploads/works/146_1.jpg",  # 原始图片
            "/uploads/works_optimized/146_1_medium.webp",  # 优化图片
            "/uploads/works/124_1.jpg",
            "/uploads/works_optimized/124_1_medium.webp",
            "/uploads/works/46_1.jpg",
            "/uploads/works_optimized/46_1_medium.webp"
        ]
        
        results = []
        
        for img_path in test_images:
            url = f"{base_url}{img_path}"
            
            try:
                start_time = time.time()
                response = requests.get(url, timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    load_time = (end_time - start_time) * 1000  # 毫秒
                    file_size = len(response.content)
                    
                    results.append({
                        'url': img_path,
                        'load_time_ms': round(load_time, 2),
                        'file_size': file_size,
                        'status': 'success'
                    })
                    print(f"  ✅ {img_path}: {load_time:.1f}ms, {self.format_size(file_size)}")
                else:
                    results.append({
                        'url': img_path,
                        'status': 'failed',
                        'error': f"HTTP {response.status_code}"
                    })
                    print(f"  ❌ {img_path}: HTTP {response.status_code}")
                    
            except Exception as e:
                results.append({
                    'url': img_path,
                    'status': 'error',
                    'error': str(e)
                })
                print(f"  ❌ {img_path}: {e}")
        
        return results
    
    def generate_report(self) -> None:
        """生成性能测试报告"""
        print("🖼️  图片压缩性能测试报告")
        print("=" * 60)
        
        # 分析压缩结果
        compression_results = self.analyze_compression_results()
        
        if compression_results['files_analyzed'] == 0:
            print("❌ 没有找到可分析的文件")
            return
        
        # 总体统计
        total_original = compression_results['total_original_size']
        total_optimized = compression_results['total_optimized_size']
        total_savings = total_original - total_optimized
        savings_percent = (total_savings / total_original * 100) if total_original > 0 else 0
        
        print(f"\n📊 总体压缩效果:")
        print(f"   原始总大小: {self.format_size(total_original)}")
        print(f"   优化后总大小: {self.format_size(total_optimized)}")
        print(f"   节省空间: {self.format_size(total_savings)} ({savings_percent:.1f}%)")
        print(f"   处理文件数: {compression_results['files_analyzed']} 个")
        
        # 按尺寸分类统计
        print(f"\n📏 按尺寸分类效果:")
        for size_type, data in compression_results['size_breakdown'].items():
            if data['count'] > 0:
                original_size = data['original']
                optimized_size = data['optimized']
                savings = original_size - optimized_size
                savings_percent = (savings / original_size * 100) if original_size > 0 else 0
                
                print(f"   {size_type.upper()}:")
                print(f"     文件数: {data['count']}")
                print(f"     原始: {self.format_size(original_size)}")
                print(f"     优化: {self.format_size(optimized_size)}")
                print(f"     节省: {self.format_size(savings)} ({savings_percent:.1f}%)")
        
        # 显示前10个文件的详细信息
        print(f"\n🔍 文件详情 (前10个):")
        for i, detail in enumerate(compression_results['compression_details'][:10]):
            original_size = detail['original_size']
            total_optimized = detail['total_optimized_size']
            savings = original_size - total_optimized
            savings_percent = (savings / original_size * 100) if original_size > 0 else 0
            
            print(f"   {i+1}. {detail['filename']}")
            print(f"      原始: {self.format_size(original_size)}")
            print(f"      优化: {self.format_size(total_optimized)}")
            print(f"      节省: {self.format_size(savings)} ({savings_percent:.1f}%)")
        
        # 测试加载性能
        try:
            loading_results = self.test_loading_performance()
            
            print(f"\n⚡ 加载性能测试:")
            original_times = []
            optimized_times = []
            
            for result in loading_results:
                if result['status'] == 'success':
                    if 'works_optimized' in result['url']:
                        optimized_times.append(result['load_time_ms'])
                    else:
                        original_times.append(result['load_time_ms'])
            
            if original_times and optimized_times:
                avg_original = sum(original_times) / len(original_times)
                avg_optimized = sum(optimized_times) / len(optimized_times)
                improvement = ((avg_original - avg_optimized) / avg_original * 100)
                
                print(f"   原始图片平均加载时间: {avg_original:.1f}ms")
                print(f"   优化图片平均加载时间: {avg_optimized:.1f}ms")
                print(f"   性能提升: {improvement:.1f}%")
            
        except Exception as e:
            print(f"   ⚠️ 加载性能测试失败: {e}")
        
        print(f"\n✅ 性能测试完成!")
        print(f"   优化后的图片位于: {self.optimized_dir}")
        print(f"   建议在生产环境中使用优化后的图片以获得最佳性能")

def main():
    """主函数"""
    tester = PerformanceTester()
    tester.generate_report()

if __name__ == "__main__":
    main()
