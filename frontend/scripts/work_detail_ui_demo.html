<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品详情页UI重新设计</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .layout-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        .image-section {
            position: relative;
        }
        .main-image {
            width: 100%;
            aspect-ratio: 4/3;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .main-image:hover {
            transform: scale(1.02);
        }
        .thumbnail-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        .thumbnail {
            aspect-ratio: 1;
            background: #e9ecef;
            border-radius: 8px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }
        .thumbnail.active {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.2);
        }
        .thumbnail:hover {
            border-color: #6c757d;
        }
        .info-section {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            height: fit-content;
        }
        .award-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: #dc3545;
            color: white;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 24px;
            font-size: 16px;
        }
        .award-badge.second { background: #fd7e14; }
        .award-badge.third { background: #ffc107; color: #212529; }
        .award-badge.excellent { background: #007bff; }
        .award-badge.finalist { background: #6c757d; }
        .work-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 12px;
            line-height: 1.3;
        }
        .architect {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-icon {
            color: #dc3545;
            margin-bottom: 8px;
        }
        .stat-icon.views { color: #007bff; }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .detail-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .detail-icon {
            color: #6c757d;
        }
        .detail-content .label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        .detail-content .value {
            font-weight: 500;
        }
        .tags-section {
            margin-bottom: 24px;
        }
        .tags-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            font-weight: 500;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .tag {
            background: #e9ecef;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            color: #495057;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .btn-primary {
            background: #dc3545;
            color: white;
        }
        .btn-primary:hover {
            background: #c82333;
        }
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .btn-outline:hover {
            background: #f8f9fa;
        }
        .description-section {
            grid-column: 1 / -1;
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .description-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            color: #495057;
            line-height: 1.7;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .comparison-item.before {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .comparison-item.after {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .comparison-title {
            font-weight: bold;
            margin-bottom: 12px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 4px 0;
            position: relative;
            padding-left: 20px;
        }
        .feature-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #007bff;
            font-weight: bold;
        }
        .highlight {
            background: #e3f2fd;
            padding: 16px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        @media (max-width: 768px) {
            .layout-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 作品详情页UI重新设计</h1>
            <p>优化图片显示布局，提升用户体验</p>
        </div>

        <div class="comparison">
            <div class="comparison-item before">
                <div class="comparison-title">❌ 修改前的问题</div>
                <ul class="feature-list">
                    <li>图片铺满整个屏幕宽度</li>
                    <li>图片比例过大，占用过多空间</li>
                    <li>信息展示不够清晰</li>
                    <li>移动端体验不佳</li>
                    <li>缺少缩略图预览</li>
                </ul>
            </div>
            <div class="comparison-item after">
                <div class="comparison-title">✅ 修改后的改进</div>
                <ul class="feature-list">
                    <li>左右分栏布局，更加合理</li>
                    <li>图片尺寸适中，4:3比例</li>
                    <li>右侧信息卡片式展示</li>
                    <li>响应式设计，移动端友好</li>
                    <li>添加缩略图网格预览</li>
                </ul>
            </div>
        </div>

        <h2>🖼️ 新的布局设计演示</h2>
        
        <div class="layout-grid">
            <!-- 左侧图片区域 -->
            <div class="image-section">
                <div class="main-image">
                    主图片区域 (4:3 比例)
                    <br>点击可放大预览
                </div>
                <div class="thumbnail-grid">
                    <div class="thumbnail active">图1</div>
                    <div class="thumbnail">图2</div>
                    <div class="thumbnail">图3</div>
                    <div class="thumbnail">+5</div>
                </div>
            </div>

            <!-- 右侧信息区域 -->
            <div class="info-section">
                <div class="award-badge">
                    🏆 一等奖
                </div>

                <div class="work-title">地下城的颂歌<br>Chants to the Undertown</div>
                <div class="architect">陈祺、常宇欣、房璐杰、梁馨月</div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">❤️</div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">点赞</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon views">👁️</div>
                        <div class="stat-number">930</div>
                        <div class="stat-label">浏览</div>
                    </div>
                </div>

                <div class="detail-card">
                    <div class="detail-icon">📅</div>
                    <div class="detail-content">
                        <div class="label">竞赛年份</div>
                        <div class="value">2024年</div>
                    </div>
                </div>

                <div class="detail-card">
                    <div class="detail-icon">📍</div>
                    <div class="detail-content">
                        <div class="label">学校/机构</div>
                        <div class="value">天津大学</div>
                    </div>
                </div>

                <div class="tags-section">
                    <div class="tags-title">标签</div>
                    <div class="tags">
                        <span class="tag">UIA-霍普杯</span>
                        <span class="tag">2024</span>
                        <span class="tag">国际竞赛</span>
                        <span class="tag">一等奖</span>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary">❤️ 点赞</button>
                    <button class="btn btn-outline">🔖 收藏</button>
                </div>
            </div>

            <!-- 作品简介区域 -->
            <div class="description-section">
                <div class="section-title">作品简介</div>
                <div class="description-content">
                    本作品参加了UIA-霍普杯2024国际大学生建筑设计竞赛，展现了优秀的建筑设计理念和创新思维。作品以"地下城的颂歌"为主题，探索了城市地下空间的可能性，通过创新的设计手法，为城市发展提供了新的思路和方向。
                    <br><br>
                    设计团队通过深入的调研和分析，结合当代城市发展的需求，提出了具有前瞻性的设计方案。作品不仅在功能性上满足了现代城市的需要，更在美学和可持续性方面展现了卓越的水准。
                </div>
            </div>
        </div>

        <div class="highlight">
            <h3>🎯 设计改进要点</h3>
            <ul class="feature-list">
                <li><strong>左右分栏布局</strong>：图片和信息分离，层次更清晰</li>
                <li><strong>合理的图片尺寸</strong>：4:3比例，不再铺满屏幕</li>
                <li><strong>缩略图网格</strong>：方便快速浏览多张图片</li>
                <li><strong>卡片式信息展示</strong>：统计数据、详细信息分块显示</li>
                <li><strong>响应式设计</strong>：移动端自动调整为单列布局</li>
                <li><strong>交互优化</strong>：悬停效果、点击反馈更加友好</li>
            </ul>
        </div>

        <h3>📱 响应式适配</h3>
        <p>新设计在不同设备上都有良好的显示效果：</p>
        <ul class="feature-list">
            <li><strong>桌面端</strong>：左右分栏，充分利用屏幕空间</li>
            <li><strong>平板端</strong>：保持分栏布局，调整间距</li>
            <li><strong>移动端</strong>：自动切换为单列布局，图片在上，信息在下</li>
        </ul>

        <div class="highlight">
            <h3>🌐 访问地址</h3>
            <p><strong>作品详情页示例：</strong> <a href="http://localhost:3000/work/146" target="_blank">http://localhost:3000/work/146</a></p>
            <p>现在可以体验全新的作品详情页布局，图片显示更加合理，信息展示更加清晰！</p>
        </div>
    </div>

    <script>
        console.log('🎨 作品详情页UI重新设计演示');
        console.log('✅ 主要改进：');
        console.log('  - 左右分栏布局');
        console.log('  - 合理的图片尺寸');
        console.log('  - 缩略图网格预览');
        console.log('  - 卡片式信息展示');
        console.log('  - 响应式设计');
        
        // 简单的交互效果
        document.querySelectorAll('.thumbnail').forEach(thumb => {
            thumb.addEventListener('click', function() {
                document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
