# 霍普杯竞赛数据导入工具

## 概述

这个工具用于将UIA-霍普杯2024国际大学生建筑设计竞赛的获奖作品数据从Excel文件导入到项目数据库中，同时复制相关的图片文件。

## 功能特性

- ✅ 从Excel文件读取竞赛数据
- ✅ 自动创建数据库表
- ✅ 复制图片文件到项目目录
- ✅ 生成详细的作品描述
- ✅ 支持多种奖项等级
- ✅ 完整的错误处理和日志记录

## 文件结构

```
scripts/
├── import_competition_data.py    # 主导入脚本
├── install_import_deps.py        # 依赖安装脚本
├── run_import.bat                # Windows批处理文件
└── README.md                     # 使用说明
```

## 使用方法

### 方法一：使用批处理文件（推荐）

1. 双击运行 `scripts/run_import.bat`
2. 脚本会自动安装依赖并执行导入

### 方法二：手动执行

1. **安装依赖**
   ```bash
   python scripts/install_import_deps.py
   ```

2. **执行导入**
   ```bash
   python scripts/import_competition_data.py
   ```

## 数据源配置

在 `scripts/import_competition_data.py` 中修改以下路径：

```python
self.excel_path = r"E:\UED\霍普杯\UIA-霍普杯2024国际大学生建筑设计竞获奖名单.xlsx12.10.xlsx"
self.images_source_dir = r"E:\UED\霍普杯\2024压缩图纸"
```

## 图片文件命名规则

图片文件应按以下格式命名：
- 作品ID为3位数字 + 图片序号2位数字 + .jpg
- 例如：作品ID=146 对应图片文件 `14601.jpg`, `14602.jpg`, `14603.jpg`

## 导入的数据结构

每个作品包含以下信息：
- **ID**: 作品编号
- **标题**: 中英文标题
- **建筑师**: 设计者姓名
- **年份**: 2024
- **奖项**: 一等奖、二等奖、三等奖、优秀奖
- **描述**: 自动生成的详细描述
- **位置**: 学校信息
- **图片**: 最多3张作品图片
- **标签**: 包含竞赛相关标签
- **状态**: 已发布、精选（一二等奖）

## 导入结果

本次导入成功处理了52个获奖作品：

### 奖项分布
- **一等奖**: 1个作品
- **二等奖**: 3个作品  
- **三等奖**: 8个作品
- **优秀奖**: 40个作品

### 文件输出
- **前端图片**: `public/uploads/works/`
- **后端图片**: `backend/uploads/works/`
- **数据库**: SQLite数据库中的 `architectural_works` 表

## 技术细节

### 依赖包
- `pandas`: Excel文件读取
- `openpyxl`: Excel文件解析
- `sqlalchemy`: 数据库操作
- `pathlib`: 路径处理

### 数据库表
脚本会自动创建以下表：
- `users`: 用户表
- `architectural_works`: 建筑作品表
- `likes`: 点赞表
- `favorites`: 收藏表
- `comments`: 评论表

### 错误处理
- 数据库连接失败检测
- 文件不存在检测
- 数据插入失败回滚
- 详细的错误日志记录

## 注意事项

1. **路径配置**: 确保Excel文件和图片目录路径正确
2. **文件权限**: 确保有读取源文件和写入目标目录的权限
3. **数据库**: 脚本会自动创建数据库表，无需手动创建
4. **重复导入**: 脚本会检查重复ID，避免重复导入
5. **图片格式**: 目前只支持JPG格式的图片文件

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确保文件没有被其他程序占用

2. **图片复制失败**
   - 检查图片目录路径
   - 确保图片文件命名符合规则

3. **数据库连接失败**
   - 确保在项目根目录运行脚本
   - 检查后端依赖是否正确安装

### 日志信息

脚本会输出详细的执行日志：
- ✅ 成功操作
- ❌ 失败操作  
- ⚠️ 警告信息

## 更新记录

- **v1.0** (2024-12-10): 初始版本，支持霍普杯2024数据导入
- 成功导入52个获奖作品
- 复制156张作品图片
- 自动生成作品描述和标签

## 联系方式

如有问题或建议，请联系开发团队。
