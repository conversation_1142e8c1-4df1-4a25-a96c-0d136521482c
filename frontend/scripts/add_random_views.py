#!/usr/bin/env python3
"""
为所有作品添加随机浏览量
一等奖、二等奖、三等奖的作品浏览量更高
"""

import sys
import random
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    import os
    os.chdir(str(backend_path))
    from app.core.database import SessionLocal
    from sqlalchemy import text
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class ViewsGenerator:
    def __init__(self):
        self.db = SessionLocal()
        
        # 不同奖项的浏览量范围
        self.view_ranges = {
            "一等奖": (800, 1000),    # 800-1000 浏览量
            "二等奖": (600, 900),     # 600-900 浏览量
            "三等奖": (400, 800),     # 400-800 浏览量
            "优秀奖": (200, 600),     # 200-600 浏览量
            "入围奖": (50, 400),      # 50-400 浏览量
        }
    
    def get_current_works(self):
        """获取所有作品及其当前浏览量"""
        try:
            result = self.db.execute(text("""
                SELECT id, title, award, views_count 
                FROM architectural_works 
                ORDER BY 
                    CASE 
                        WHEN award = '一等奖' THEN 1
                        WHEN award = '二等奖' THEN 2
                        WHEN award = '三等奖' THEN 3
                        WHEN award = '优秀奖' THEN 4
                        WHEN award = '入围奖' THEN 5
                        ELSE 6
                    END,
                    id
            """))
            
            works = result.fetchall()
            return works
        except Exception as e:
            print(f"获取作品失败: {e}")
            return []
    
    def generate_random_views(self, award):
        """根据奖项等级生成随机浏览量"""
        if award in self.view_ranges:
            min_views, max_views = self.view_ranges[award]
            return random.randint(min_views, max_views)
        else:
            # 未知奖项，使用默认范围
            return random.randint(50, 300)
    
    def update_views_count(self, work_id, new_views):
        """更新作品的浏览量"""
        try:
            self.db.execute(text("""
                UPDATE architectural_works 
                SET views_count = :views_count 
                WHERE id = :work_id
            """), {
                "views_count": new_views,
                "work_id": work_id
            })
            return True
        except Exception as e:
            print(f"更新作品 {work_id} 浏览量失败: {e}")
            return False
    
    def add_random_views_to_all(self):
        """为所有作品添加随机浏览量"""
        print("📊 开始为所有作品添加随机浏览量...")
        print("=" * 60)
        
        works = self.get_current_works()
        if not works:
            print("❌ 没有找到作品")
            return
        
        print(f"找到 {len(works)} 个作品")
        print()
        
        # 按奖项分组统计
        award_stats = {}
        updated_count = 0
        
        for work_id, title, award, current_views in works:
            # 生成新的浏览量
            new_views = self.generate_random_views(award)
            
            # 更新数据库
            if self.update_views_count(work_id, new_views):
                print(f"✅ ID {work_id}: {title[:30]}... ({award}) - {current_views or 0} → {new_views} 浏览量")
                updated_count += 1
                
                # 统计
                if award not in award_stats:
                    award_stats[award] = {"count": 0, "total_views": 0, "min_views": float('inf'), "max_views": 0}
                
                award_stats[award]["count"] += 1
                award_stats[award]["total_views"] += new_views
                award_stats[award]["min_views"] = min(award_stats[award]["min_views"], new_views)
                award_stats[award]["max_views"] = max(award_stats[award]["max_views"], new_views)
            else:
                print(f"❌ ID {work_id}: 更新失败")
        
        # 提交更改
        try:
            self.db.commit()
            print(f"\n✅ 成功更新了 {updated_count} 个作品的浏览量")
        except Exception as e:
            self.db.rollback()
            print(f"\n❌ 提交失败: {e}")
            return
        
        # 显示统计信息
        self.show_statistics(award_stats)
    
    def show_statistics(self, award_stats):
        """显示浏览量统计信息"""
        print("\n📈 浏览量统计信息")
        print("=" * 50)
        
        # 按奖项等级排序
        award_order = ["一等奖", "二等奖", "三等奖", "优秀奖", "入围奖"]
        
        for award in award_order:
            if award in award_stats:
                stats = award_stats[award]
                avg_views = stats["total_views"] // stats["count"]
                
                print(f"{award}:")
                print(f"  作品数量: {stats['count']} 个")
                print(f"  浏览量范围: {stats['min_views']} - {stats['max_views']}")
                print(f"  平均浏览量: {avg_views}")
                print(f"  总浏览量: {stats['total_views']}")
                print()
        
        # 总计
        total_works = sum(stats["count"] for stats in award_stats.values())
        total_views = sum(stats["total_views"] for stats in award_stats.values())
        overall_avg = total_views // total_works if total_works > 0 else 0
        
        print(f"📊 总计:")
        print(f"  总作品数: {total_works} 个")
        print(f"  总浏览量: {total_views}")
        print(f"  平均浏览量: {overall_avg}")
    
    def show_top_works(self, limit=10):
        """显示浏览量最高的作品"""
        print(f"\n🏆 浏览量最高的 {limit} 个作品")
        print("=" * 50)
        
        try:
            result = self.db.execute(text("""
                SELECT id, title, award, views_count 
                FROM architectural_works 
                ORDER BY views_count DESC 
                LIMIT :limit
            """), {"limit": limit})
            
            top_works = result.fetchall()
            
            for i, (work_id, title, award, views) in enumerate(top_works, 1):
                print(f"{i:2d}. {title[:40]:<40} ({award}) - {views} 浏览量")
                
        except Exception as e:
            print(f"查询失败: {e}")
    
    def verify_results(self):
        """验证结果"""
        print("\n🔍 验证浏览量分布...")
        print("=" * 40)
        
        try:
            result = self.db.execute(text("""
                SELECT award, COUNT(*) as count, 
                       MIN(views_count) as min_views,
                       MAX(views_count) as max_views,
                       AVG(views_count) as avg_views
                FROM architectural_works 
                GROUP BY award
                ORDER BY 
                    CASE 
                        WHEN award = '一等奖' THEN 1
                        WHEN award = '二等奖' THEN 2
                        WHEN award = '三等奖' THEN 3
                        WHEN award = '优秀奖' THEN 4
                        WHEN award = '入围奖' THEN 5
                        ELSE 6
                    END
            """))
            
            results = result.fetchall()
            
            for award, count, min_views, max_views, avg_views in results:
                print(f"{award}: {count} 个作品, 浏览量 {min_views}-{max_views} (平均 {int(avg_views)})")
                
        except Exception as e:
            print(f"验证失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        self.db.close()

def main():
    """主函数"""
    print("📊 作品浏览量生成器")
    print("=" * 60)
    print("为霍普杯建筑设计竞赛作品添加随机浏览量")
    print("一等奖、二等奖、三等奖作品将获得更高的浏览量")
    print("=" * 60)
    
    generator = ViewsGenerator()
    
    try:
        # 显示当前状态
        works = generator.get_current_works()
        if works:
            current_views = [work[3] or 0 for work in works]
            total_current = sum(current_views)
            print(f"当前总浏览量: {total_current}")
            
            if total_current > 0:
                print("\n是否要重新生成所有作品的浏览量？(y/N): ", end="")
                choice = input().strip().lower()
                if choice not in ['y', 'yes']:
                    print("取消操作")
                    return
        
        # 生成随机浏览量
        generator.add_random_views_to_all()
        
        # 显示结果
        generator.show_top_works(15)
        generator.verify_results()
        
        print("\n🎉 浏览量生成完成！")
        print("现在所有作品都有了合理的浏览量，获奖等级高的作品浏览量更多。")
        
    finally:
        generator.close()

if __name__ == "__main__":
    main()
