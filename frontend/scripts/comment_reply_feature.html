<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论回复功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            background: #f8f9fa;
        }
        .feature-card.before {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .feature-card.after {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 24px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #007bff;
            font-weight: bold;
            font-size: 16px;
        }
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e9ecef;
            margin-bottom: 30px;
        }
        .comment-demo {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #f8f9fa;
        }
        .comment-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .comment-meta {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .username {
            font-weight: 600;
            color: #333;
        }
        .time {
            font-size: 12px;
            color: #666;
        }
        .reply-to {
            font-size: 12px;
            color: #007bff;
        }
        .comment-content {
            margin-bottom: 12px;
            color: #333;
        }
        .comment-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .reply-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .reply-btn:hover {
            background: #f0f0f0;
            color: #333;
        }
        .reply-count {
            font-size: 12px;
            color: #666;
        }
        .nested-comments {
            margin-left: 32px;
            border-left: 2px solid #e9ecef;
            padding-left: 16px;
            margin-top: 16px;
        }
        .reply-form {
            margin-top: 12px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .reply-input {
            width: 100%;
            min-height: 60px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            margin-bottom: 8px;
        }
        .reply-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn-cancel {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #ddd;
        }
        .btn-cancel:hover {
            background: #e9ecef;
        }
        .btn-submit {
            background: #007bff;
            color: white;
        }
        .btn-submit:hover {
            background: #0056b3;
        }
        .highlight {
            background: #e3f2fd;
            padding: 16px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .tech-specs {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 12px 0;
        }
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
        }
        @media (max-width: 768px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            .nested-comments {
                margin-left: 16px;
                padding-left: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💬 评论回复功能实现</h1>
            <p>为霍普杯建筑设计竞赛网站添加嵌套评论回复功能</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card before">
                <div class="feature-title">
                    🔄 改进前
                </div>
                <ul class="feature-list">
                    <li>只能发表新评论</li>
                    <li>无法回复已有评论</li>
                    <li>评论结构扁平化</li>
                    <li>缺少互动性</li>
                    <li>讨论不够深入</li>
                </ul>
            </div>
            <div class="feature-card after">
                <div class="feature-title">
                    ✅ 改进后
                </div>
                <ul class="feature-list">
                    <li>支持嵌套回复评论</li>
                    <li>最大3层嵌套深度</li>
                    <li>显示回复对象用户名</li>
                    <li>回复数量统计</li>
                    <li>增强用户互动体验</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 功能演示</h2>
            
            <!-- 主评论 -->
            <div class="comment-demo">
                <div class="comment-header">
                    <div class="avatar">张</div>
                    <div class="comment-meta">
                        <span class="username">张建筑师</span>
                        <span class="time">2小时前</span>
                    </div>
                </div>
                <div class="comment-content">
                    这个设计理念很有创意，特别是对传统竹筒屋的现代化改造，既保留了文化特色又满足了现代居住需求。
                </div>
                <div class="comment-actions">
                    <button class="reply-btn" onclick="toggleReply('reply1')">
                        <span class="icon">↩️</span> 回复
                    </button>
                    <span class="reply-count">2 条回复</span>
                </div>
                
                <div id="reply1" class="reply-form" style="display: none;">
                    <div style="display: flex; gap: 8px; align-items: flex-start;">
                        <div class="avatar" style="width: 24px; height: 24px; font-size: 12px;">我</div>
                        <div style="flex: 1;">
                            <textarea class="reply-input" placeholder="回复 @张建筑师..."></textarea>
                            <div class="reply-actions">
                                <button class="btn btn-cancel" onclick="toggleReply('reply1')">取消</button>
                                <button class="btn btn-submit">发布回复</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 嵌套回复 -->
                <div class="nested-comments">
                    <div class="comment-demo">
                        <div class="comment-header">
                            <div class="avatar">李</div>
                            <div class="comment-meta">
                                <span class="username">李设计师</span>
                                <span class="reply-to">回复 @张建筑师</span>
                                <span class="time">1小时前</span>
                            </div>
                        </div>
                        <div class="comment-content">
                            同意！这种设计思路值得推广，在保护传统建筑的同时也考虑了实用性。
                        </div>
                        <div class="comment-actions">
                            <button class="reply-btn" onclick="toggleReply('reply2')">
                                <span class="icon">↩️</span> 回复
                            </button>
                        </div>
                        
                        <div id="reply2" class="reply-form" style="display: none;">
                            <div style="display: flex; gap: 8px; align-items: flex-start;">
                                <div class="avatar" style="width: 24px; height: 24px; font-size: 12px;">我</div>
                                <div style="flex: 1;">
                                    <textarea class="reply-input" placeholder="回复 @李设计师..."></textarea>
                                    <div class="reply-actions">
                                        <button class="btn btn-cancel" onclick="toggleReply('reply2')">取消</button>
                                        <button class="btn btn-submit">发布回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 三级回复 -->
                        <div class="nested-comments">
                            <div class="comment-demo">
                                <div class="comment-header">
                                    <div class="avatar">王</div>
                                    <div class="comment-meta">
                                        <span class="username">王同学</span>
                                        <span class="reply-to">回复 @李设计师</span>
                                        <span class="time">30分钟前</span>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    确实，这种平衡传统与现代的设计理念在当今建筑界很重要。
                                </div>
                                <div class="comment-actions">
                                    <span style="font-size: 12px; color: #666;">已达到最大回复深度</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="highlight">
            <h3>🎯 核心功能特性</h3>
            <ul class="feature-list">
                <li><strong>嵌套回复</strong>：支持最多3层嵌套回复，避免过深的层级</li>
                <li><strong>回复标识</strong>：清晰显示回复对象的用户名</li>
                <li><strong>视觉层次</strong>：通过缩进和边框线区分回复层级</li>
                <li><strong>回复统计</strong>：显示每条评论的回复数量</li>
                <li><strong>交互优化</strong>：点击回复按钮展开回复表单</li>
                <li><strong>响应式设计</strong>：移动端适配良好</li>
            </ul>
        </div>

        <div class="tech-specs">
            <h3>🔧 技术实现</h3>
            
            <h4>1. 数据结构更新</h4>
            <div class="code-block">
export interface Comment {
  id: number
  work_id: number
  user_id: number
  username: string
  user_avatar?: string
  content: string
  created_at: string
  is_approved: boolean
  parent_id?: number        // 新增：父评论ID
  replies?: Comment[]       // 新增：子回复数组
  reply_to_username?: string // 新增：回复对象用户名
}
            </div>

            <h4>2. API接口扩展</h4>
            <div class="code-block">
// 创建评论（支持回复）
async createComment(workId: string, data: { 
  content: string; 
  parent_id?: number 
}) {
  return this.request(`/works/${workId}/comments`, {
    method: "POST",
    body: JSON.stringify(data),
  })
}

// 专门的回复接口
async replyToComment(workId: string, commentId: number, data: { 
  content: string 
}) {
  return this.request(`/works/${workId}/comments/${commentId}/reply`, {
    method: "POST",
    body: JSON.stringify(data),
  })
}
            </div>

            <h4>3. 组件功能</h4>
            <ul class="feature-list">
                <li><strong>评论组织</strong>：将扁平评论列表转换为嵌套结构</li>
                <li><strong>递归渲染</strong>：CommentItem组件支持递归渲染子回复</li>
                <li><strong>状态管理</strong>：管理回复表单的显示/隐藏状态</li>
                <li><strong>深度限制</strong>：最大嵌套深度为3层，避免过深层级</li>
                <li><strong>总数统计</strong>：递归计算包含回复在内的总评论数</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>🌐 访问测试</h3>
            <p><strong>更新后的作品详情页：</strong> <a href="http://localhost:3001/work/146" target="_blank">http://localhost:3001/work/146</a></p>
            <p>现在用户可以：</p>
            <ul class="feature-list">
                <li>对任何评论进行回复</li>
                <li>查看嵌套的回复结构</li>
                <li>看到回复对象的用户名</li>
                <li>享受更好的评论互动体验</li>
            </ul>
        </div>
    </div>

    <script>
        function toggleReply(replyId) {
            const replyForm = document.getElementById(replyId);
            if (replyForm.style.display === 'none') {
                replyForm.style.display = 'block';
                replyForm.querySelector('textarea').focus();
            } else {
                replyForm.style.display = 'none';
            }
        }

        console.log('💬 评论回复功能演示');
        console.log('✅ 主要功能：');
        console.log('  - 嵌套回复支持（最大3层）');
        console.log('  - 回复对象标识');
        console.log('  - 视觉层次区分');
        console.log('  - 回复数量统计');
        console.log('  - 交互体验优化');
    </script>
</body>
</html>
