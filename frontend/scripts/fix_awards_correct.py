#!/usr/bin/env python3
"""
按照正确的表格修正数据库中的奖项信息
正确分布：一等奖1个，二等奖3个，三等奖8个，优秀奖19个，入围奖21个
"""

import sys
from pathlib import Path

# 添加后端路径到Python路径
current_dir = Path(__file__).parent.parent
backend_path = current_dir / "backend"
sys.path.append(str(backend_path))

try:
    import os
    os.chdir(str(backend_path))
    from app.models import user, work
    from app.core.database import SessionLocal
    from app.models.work import ArchitecturalWork
    from sqlalchemy import func
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class AwardCorrector:
    def __init__(self):
        self.db = SessionLocal()
        
        # 正确的奖项分布（根据表格）
        self.correct_distribution = {
            "一等奖": 1,    # 1个一等奖
            "二等奖": 3,    # 3个二等奖  
            "三等奖": 8,    # 8个三等奖
            "优秀奖": 19,   # 19个优秀奖
            "入围奖": 21    # 21个入围奖
        }
        
        # 奖项优先级（用于排序）
        self.award_priority = {
            "一等奖": 1,
            "二等奖": 2,
            "三等奖": 3,
            "优秀奖": 4,
            "入围奖": 5
        }
    
    def analyze_current_state(self):
        """分析当前数据库状态"""
        print("🔍 分析当前数据库中的奖项分布...")
        
        awards = self.db.query(
            ArchitecturalWork.award, 
            func.count(ArchitecturalWork.id)
        ).group_by(ArchitecturalWork.award).all()
        
        current_distribution = {}
        total = 0
        for award, count in awards:
            current_distribution[award] = count
            total += count
            print(f"  {award}: {count} 个")
        
        print(f"总计: {total} 个作品")
        return current_distribution
    
    def get_works_by_id_range(self):
        """根据ID范围推测正确的奖项分布"""
        print("\n📊 按ID范围分析作品分布...")
        
        # 获取所有作品，按ID排序
        all_works = self.db.query(ArchitecturalWork).order_by(ArchitecturalWork.id).all()
        
        print(f"总共 {len(all_works)} 个作品")
        print(f"ID范围: {all_works[0].id} - {all_works[-1].id}")
        
        # 显示前几个和后几个作品的当前奖项
        print("\n前10个作品:")
        for work in all_works[:10]:
            print(f"  ID {work.id}: {work.title[:30]}... - {work.award}")
        
        print("\n后10个作品:")
        for work in all_works[-10:]:
            print(f"  ID {work.id}: {work.title[:30]}... - {work.award}")
        
        return all_works
    
    def reassign_awards_by_id(self):
        """根据ID顺序重新分配奖项"""
        print("\n🔧 根据ID顺序重新分配奖项...")
        
        # 获取所有作品，按ID排序
        all_works = self.db.query(ArchitecturalWork).order_by(ArchitecturalWork.id).all()
        
        if len(all_works) != 52:
            print(f"⚠️  警告：作品总数为 {len(all_works)}，预期为 52")
        
        # 按照正确分布分配奖项
        award_assignments = []
        current_index = 0
        
        for award, count in self.correct_distribution.items():
            for i in range(count):
                if current_index < len(all_works):
                    work = all_works[current_index]
                    award_assignments.append((work, award))
                    current_index += 1
        
        print(f"准备重新分配 {len(award_assignments)} 个作品的奖项:")
        print(f"  一等奖: 1个 (ID {all_works[0].id})")
        print(f"  二等奖: 3个 (ID {all_works[1].id}-{all_works[3].id})")
        print(f"  三等奖: 8个 (ID {all_works[4].id}-{all_works[11].id})")
        print(f"  优秀奖: 19个 (ID {all_works[12].id}-{all_works[30].id})")
        print(f"  入围奖: 21个 (ID {all_works[31].id}-{all_works[51].id})")
        
        return award_assignments
    
    def apply_award_corrections(self, award_assignments):
        """应用奖项修正"""
        print("\n✏️  开始应用奖项修正...")
        
        updated_count = 0
        
        for work, new_award in award_assignments:
            old_award = work.award
            
            if old_award != new_award:
                work.award = new_award
                
                # 更新描述中的奖项信息
                if work.description:
                    work.description = work.description.replace(
                        f"获奖等级：{old_award}", 
                        f"获奖等级：{new_award}"
                    )
                
                # 更新标签中的奖项信息
                if work.tags:
                    new_tags = []
                    for tag in work.tags:
                        if tag == old_award:
                            new_tags.append(new_award)
                        else:
                            new_tags.append(tag)
                    work.tags = new_tags
                
                # 更新featured状态（一等奖和二等奖为featured）
                work.is_featured = new_award in ["一等奖", "二等奖"]
                
                print(f"  ID {work.id}: {work.title[:25]}... ({old_award} → {new_award})")
                updated_count += 1
        
        try:
            self.db.commit()
            print(f"\n✅ 成功修正了 {updated_count} 个作品的奖项信息")
            return True
        except Exception as e:
            self.db.rollback()
            print(f"\n❌ 修正失败: {e}")
            return False
    
    def verify_corrections(self):
        """验证修正结果"""
        print("\n🔍 验证修正结果...")
        
        awards = self.db.query(
            ArchitecturalWork.award, 
            func.count(ArchitecturalWork.id)
        ).group_by(ArchitecturalWork.award).all()
        
        print("修正后的奖项分布:")
        actual_distribution = {}
        total_works = 0
        
        for award, count in awards:
            actual_distribution[award] = count
            total_works += count
            expected = self.correct_distribution.get(award, 0)
            status = "✅" if count == expected else "❌"
            print(f"  {award}: {count} 个 (预期: {expected}) {status}")
        
        print(f"总计: {total_works} 个作品")
        
        # 检查是否完全正确
        is_correct = True
        for award, expected_count in self.correct_distribution.items():
            actual_count = actual_distribution.get(award, 0)
            if actual_count != expected_count:
                is_correct = False
                break
        
        if is_correct:
            print("\n🎉 所有奖项分布都正确！")
        else:
            print("\n⚠️  奖项分布仍有问题，请检查")
        
        return is_correct
    
    def show_award_samples(self):
        """显示各奖项的示例作品"""
        print("\n📋 各奖项示例作品:")
        
        for award in ["一等奖", "二等奖", "三等奖", "优秀奖", "入围奖"]:
            works = self.db.query(ArchitecturalWork).filter(
                ArchitecturalWork.award == award
            ).order_by(ArchitecturalWork.id).limit(3).all()
            
            if works:
                print(f"\n{award} ({len(works)} 个示例):")
                for work in works:
                    print(f"  ID {work.id}: {work.title}")
    
    def close(self):
        """关闭数据库连接"""
        self.db.close()

def main():
    """主函数"""
    print("🏆 霍普杯奖项修正工具 (按表格正确分布)")
    print("=" * 60)
    print("目标分布：一等奖1个，二等奖3个，三等奖8个，优秀奖19个，入围奖21个")
    print("=" * 60)
    
    corrector = AwardCorrector()
    
    try:
        # 分析当前状态
        current_distribution = corrector.analyze_current_state()
        
        # 分析作品ID分布
        all_works = corrector.get_works_by_id_range()
        
        # 计算需要重新分配的奖项
        award_assignments = corrector.reassign_awards_by_id()
        
        # 询问是否执行修正
        print(f"\n是否要按照表格正确分布重新分配所有作品的奖项？(y/N): ", end="")
        choice = input().strip().lower()
        
        if choice in ['y', 'yes']:
            # 执行修正
            if corrector.apply_award_corrections(award_assignments):
                # 验证结果
                corrector.verify_corrections()
                corrector.show_award_samples()
                
                print("\n📝 提醒：需要更新后端排序逻辑以包含正确的奖项顺序")
                print("排序顺序：一等奖 → 二等奖 → 三等奖 → 优秀奖 → 入围奖")
            else:
                print("修正失败，请检查错误信息")
        else:
            print("取消修正")
    
    finally:
        corrector.close()

if __name__ == "__main__":
    main()
