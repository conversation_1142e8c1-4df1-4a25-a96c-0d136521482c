<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
        .feature-card h3 {
            margin-top: 0;
            color: #495057;
        }
        .button {
            display: inline-block;
            padding: 8px 16px;
            margin: 4px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            color: white;
        }
        .btn-admin { background: #6c757d; }
        .btn-disable { background: #ffc107; color: #212529; }
        .btn-enable { background: #28a745; }
        .btn-delete { background: #dc3545; }
        .btn-primary { background: #007bff; }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-admin { background: #dc3545; color: white; }
        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .user-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-details h4 {
            margin: 0 0 5px 0;
        }
        .user-details p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .user-actions {
            display: flex;
            gap: 8px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 用户管理功能增强</h1>
            <p>为管理后台的用户管理页面新增了用户删除和禁用功能</p>
        </div>

        <div class="success">
            <h3>🎉 功能已完成！</h3>
            <p>用户管理页面现在支持完整的用户管理操作，包括权限管理、状态控制和用户删除。</p>
        </div>

        <h2>🆕 新增功能</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🚫 用户禁用/启用</h3>
                <p>管理员可以禁用或启用用户账户，禁用后用户无法登录系统。</p>
                <div class="user-actions">
                    <button class="button btn-disable">🚫 禁用</button>
                    <button class="button btn-enable">✅ 启用</button>
                </div>
                <p><strong>API端点：</strong> POST /api/admin/users/{id}/toggle-active</p>
            </div>

            <div class="feature-card">
                <h3>🗑️ 用户删除</h3>
                <p>彻底删除用户及其所有相关数据，包括评论、点赞和收藏记录。</p>
                <div class="user-actions">
                    <button class="button btn-delete">🗑️ 删除</button>
                </div>
                <p><strong>API端点：</strong> DELETE /api/admin/users/{id}</p>
            </div>

            <div class="feature-card">
                <h3>🛡️ 安全保护</h3>
                <p>防止管理员意外操作自己的账号，确保系统安全。</p>
                <ul>
                    <li>不能禁用自己的账号</li>
                    <li>不能删除自己的账号</li>
                    <li>操作前需要确认</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🔄 级联删除</h3>
                <p>删除用户时自动清理相关数据，保持数据库一致性。</p>
                <ul>
                    <li>删除用户评论</li>
                    <li>删除点赞记录</li>
                    <li>删除收藏记录</li>
                </ul>
            </div>
        </div>

        <h2>👤 用户卡片示例</h2>
        <div class="user-card">
            <div class="user-info">
                <div class="user-details">
                    <h4>张三 <span class="status-badge status-admin">管理员</span> <span class="status-badge status-active">活跃</span></h4>
                    <p><EMAIL></p>
                    <p>注册时间: 2024-01-15</p>
                </div>
                <div class="user-actions">
                    <button class="button btn-admin">🛡️ 取消管理员</button>
                    <button class="button btn-disable">🚫 禁用</button>
                    <button class="button btn-delete">🗑️ 删除</button>
                </div>
            </div>
        </div>

        <div class="user-card">
            <div class="user-details">
                <h4>李四 <span class="status-badge status-inactive">禁用</span></h4>
                <p><EMAIL></p>
                <p>注册时间: 2024-02-20</p>
            </div>
            <div class="user-actions">
                <button class="button btn-admin">🛡️ 设为管理员</button>
                <button class="button btn-enable">✅ 启用</button>
                <button class="button btn-delete">🗑️ 删除</button>
            </div>
        </div>

        <h2>🔧 技术实现</h2>
        
        <h3>后端API增强</h3>
        <div class="code-block">
# 新增的后端API端点

@router.post("/users/{user_id}/toggle-active")
def toggle_user_active(user_id: int, ...):
    """切换用户激活状态（禁用/启用）"""
    # 防止管理员禁用自己
    # 切换用户激活状态
    
@router.delete("/users/{user_id}")
def delete_user(user_id: int, ...):
    """删除用户及相关数据"""
    # 防止管理员删除自己
    # 级联删除相关数据
        </div>

        <h3>前端功能增强</h3>
        <div class="code-block">
// 新增的前端处理函数

const handleToggleActive = async (userId, currentStatus) => {
  // 调用禁用/启用API
  // 显示成功/失败提示
  // 刷新用户列表
}

const handleDeleteUser = async (userId, username) => {
  // 显示确认对话框
  // 调用删除API
  // 显示操作结果
}
        </div>

        <h3>前端API路由</h3>
        <div class="code-block">
app/api/admin/users/[id]/toggle-active/route.ts  # 切换激活状态
app/api/admin/users/[id]/route.ts                # 删除用户
app/api/admin/users/[id]/toggle-admin/route.ts   # 切换管理员权限
        </div>

        <h2>🎯 用户体验改进</h2>
        <ul>
            <li><strong>直观的操作按钮</strong>：每个用户卡片都有清晰的操作按钮</li>
            <li><strong>状态标识</strong>：用户状态通过颜色标签清晰显示</li>
            <li><strong>确认对话框</strong>：危险操作前会要求用户确认</li>
            <li><strong>即时反馈</strong>：操作完成后立即显示成功或失败提示</li>
            <li><strong>安全保护</strong>：防止管理员误操作自己的账号</li>
            <li><strong>响应式设计</strong>：在不同设备上都有良好的显示效果</li>
        </ul>

        <h2>🌐 访问地址</h2>
        <div class="highlight">
            <p><strong>管理后台用户管理：</strong> <a href="http://localhost:3000/admin/users" target="_blank">http://localhost:3000/admin/users</a></p>
            <p>使用管理员账号登录后即可体验新的用户管理功能。</p>
        </div>

        <h2>📋 操作说明</h2>
        <ol>
            <li><strong>登录管理后台</strong>：使用管理员账号登录</li>
            <li><strong>进入用户管理</strong>：点击侧边栏的"用户管理"</li>
            <li><strong>查看用户列表</strong>：所有用户及其状态一目了然</li>
            <li><strong>管理用户权限</strong>：点击"设为管理员"或"取消管理员"</li>
            <li><strong>控制用户状态</strong>：点击"禁用"或"启用"按钮</li>
            <li><strong>删除用户</strong>：点击"删除"按钮，确认后执行</li>
        </ol>

        <div class="success">
            <h3>✅ 功能完成总结</h3>
            <p>用户管理功能已全面增强，现在管理员可以：</p>
            <ul>
                <li>✅ 查看所有用户及其状态</li>
                <li>✅ 设置或取消用户的管理员权限</li>
                <li>✅ 禁用或启用用户账户</li>
                <li>✅ 删除用户及其所有相关数据</li>
                <li>✅ 享受安全保护和用户友好的操作体验</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('👥 用户管理功能演示页面加载完成');
        console.log('🆕 新增功能：');
        console.log('  ✅ 用户禁用/启用');
        console.log('  ✅ 用户删除');
        console.log('  ✅ 安全保护');
        console.log('  ✅ 级联删除');
        console.log('🌐 访问地址：http://localhost:3000/admin/users');
    </script>
</body>
</html>
