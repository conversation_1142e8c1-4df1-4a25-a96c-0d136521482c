import { useState, useEffect, useRef, useCallback } from 'react';

interface UseImageLazyLoadOptions {
  threshold?: number;
  rootMargin?: string;
  fallbackSrc?: string;
  retryCount?: number;
  retryDelay?: number;
}

interface UseImageLazyLoadReturn {
  imgRef: React.RefObject<HTMLImageElement>;
  isLoaded: boolean;
  isLoading: boolean;
  hasError: boolean;
  retry: () => void;
}

/**
 * 图片懒加载Hook
 * 功能：
 * 1. 基于Intersection Observer的懒加载
 * 2. 自动重试机制
 * 3. 加载状态管理
 * 4. 错误处理
 */
export const useImageLazyLoad = (
  src: string,
  options: UseImageLazyLoadOptions = {}
): UseImageLazyLoadReturn => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    fallbackSrc,
    retryCount = 3,
    retryDelay = 1000
  } = options;

  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentRetryCount, setCurrentRetryCount] = useState(0);
  
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 加载图片
  const loadImage = useCallback((imageSrc: string) => {
    if (!imageSrc) return;

    setIsLoading(true);
    setHasError(false);

    const img = new Image();
    
    img.onload = () => {
      setIsLoaded(true);
      setIsLoading(false);
      setHasError(false);
      setCurrentRetryCount(0);
      
      // 更新实际的img元素
      if (imgRef.current) {
        imgRef.current.src = imageSrc;
        imgRef.current.style.opacity = '1';
      }
    };

    img.onerror = () => {
      setIsLoading(false);
      setHasError(true);
      
      // 尝试重试或使用fallback
      if (currentRetryCount < retryCount) {
        retryTimeoutRef.current = setTimeout(() => {
          setCurrentRetryCount(prev => prev + 1);
          loadImage(imageSrc);
        }, retryDelay);
      } else if (fallbackSrc && imageSrc !== fallbackSrc) {
        // 使用fallback图片
        loadImage(fallbackSrc);
      }
    };

    img.src = imageSrc;
  }, [currentRetryCount, retryCount, retryDelay, fallbackSrc]);

  // 手动重试
  const retry = useCallback(() => {
    setCurrentRetryCount(0);
    loadImage(src);
  }, [src, loadImage]);

  // 设置Intersection Observer
  useEffect(() => {
    if (!imgRef.current || !src) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isLoaded && !isLoading) {
            loadImage(src);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current = observer;
    observer.observe(imgRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [src, threshold, rootMargin, isLoaded, isLoading, loadImage]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    imgRef,
    isLoaded,
    isLoading,
    hasError,
    retry
  };
};

/**
 * 预加载图片Hook
 */
export const useImagePreload = (urls: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());

  const preloadImage = useCallback((url: string) => {
    if (loadedImages.has(url) || loadingImages.has(url)) {
      return Promise.resolve();
    }

    setLoadingImages(prev => new Set(prev).add(url));

    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        setLoadedImages(prev => new Set(prev).add(url));
        setLoadingImages(prev => {
          const newSet = new Set(prev);
          newSet.delete(url);
          return newSet;
        });
        resolve();
      };

      img.onerror = () => {
        setLoadingImages(prev => {
          const newSet = new Set(prev);
          newSet.delete(url);
          return newSet;
        });
        reject(new Error(`Failed to load image: ${url}`));
      };

      img.src = url;
    });
  }, [loadedImages, loadingImages]);

  const preloadImages = useCallback(async (imageUrls: string[]) => {
    const promises = imageUrls.map(url => preloadImage(url));
    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.warn('Some images failed to preload:', error);
    }
  }, [preloadImage]);

  useEffect(() => {
    if (urls.length > 0) {
      preloadImages(urls);
    }
  }, [urls, preloadImages]);

  return {
    loadedImages,
    loadingImages,
    preloadImage,
    preloadImages
  };
};

/**
 * 图片尺寸检测Hook
 */
export const useImageDimensions = (src: string) => {
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
    aspectRatio: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!src) return;

    setIsLoading(true);
    const img = new Image();

    img.onload = () => {
      setDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight
      });
      setIsLoading(false);
    };

    img.onerror = () => {
      setDimensions(null);
      setIsLoading(false);
    };

    img.src = src;
  }, [src]);

  return { dimensions, isLoading };
};
