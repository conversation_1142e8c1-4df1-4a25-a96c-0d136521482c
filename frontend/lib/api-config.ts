/**
 * 统一的 API 配置
 * 解决反向代理环境下的 API 调用问题
 */

/**
 * 获取后端 API 基础 URL
 * 在反向代理环境下，前端 API 路由应该直接调用后端服务
 */
export const getBackendApiUrl = (): string => {
  // 在前端 API 路由中（服务端），直接调用后端服务
  // 这里始终使用容器内部通信地址
  return "http://localhost:8000/api"
}

/**
 * 获取前端 API 代理 URL
 * 用于客户端代码调用前端 API 路由
 */
export const getFrontendApiUrl = (): string => {
  // 客户端代码应该调用前端 API 路由，由前端路由代理到后端
  return "/api"
}

/**
 * 获取上传文件的基础 URL
 */
export const getUploadsBaseUrl = (): string => {
  // 在生产环境中，上传文件通过反向代理访问
  if (typeof window !== "undefined") {
    // 客户端环境：使用相对路径
    return "/uploads"
  }
  
  // 服务端环境：直接访问后端
  return "http://localhost:8000/uploads"
}

/**
 * 检查是否在生产环境
 */
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === "production"
}

/**
 * 检查是否在浏览器环境
 */
export const isBrowser = (): boolean => {
  return typeof window !== "undefined"
}

/**
 * 获取当前环境信息（用于调试）
 */
export const getEnvironmentInfo = () => {
  return {
    nodeEnv: process.env.NODE_ENV,
    isBrowser: isBrowser(),
    isProduction: isProduction(),
    nextPublicApiUrl: process.env.NEXT_PUBLIC_API_URL,
    backendApiUrl: getBackendApiUrl(),
    frontendApiUrl: getFrontendApiUrl(),
    uploadsBaseUrl: getUploadsBaseUrl(),
  }
}
