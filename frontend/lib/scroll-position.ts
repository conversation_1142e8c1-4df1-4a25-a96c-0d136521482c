/**
 * 滚动位置管理工具
 * 用于保存和恢复页面滚动位置
 */

interface ScrollPosition {
  x: number
  y: number
  timestamp: number
}

const SCROLL_STORAGE_KEY = 'page_scroll_positions'
const POSITION_EXPIRY = 30 * 60 * 1000 // 30分钟过期

class ScrollPositionManager {
  private positions: Map<string, ScrollPosition> = new Map()

  constructor() {
    // 只在客户端环境下加载存储
    if (typeof window !== 'undefined') {
      this.loadFromStorage()
    }
  }

  /**
   * 保存当前页面的滚动位置
   */
  savePosition(key: string, x?: number, y?: number) {
    // 只在客户端环境下执行
    if (typeof window === 'undefined') return

    const scrollX = x ?? window.scrollX
    const scrollY = y ?? window.scrollY

    const position: ScrollPosition = {
      x: scrollX,
      y: scrollY,
      timestamp: Date.now()
    }

    this.positions.set(key, position)
    this.saveToStorage()
  }

  /**
   * 恢复指定页面的滚动位置
   */
  restorePosition(key: string, smooth: boolean = true): boolean {
    // 只在客户端环境下执行
    if (typeof window === 'undefined') return false

    const position = this.positions.get(key)

    if (!position) {
      return false
    }

    // 检查位置是否过期
    if (Date.now() - position.timestamp > POSITION_EXPIRY) {
      this.positions.delete(key)
      this.saveToStorage()
      return false
    }

    // 恢复滚动位置
    if (smooth) {
      window.scrollTo({
        left: position.x,
        top: position.y,
        behavior: 'smooth'
      })
    } else {
      window.scrollTo(position.x, position.y)
    }

    return true
  }

  /**
   * 获取指定页面的滚动位置
   */
  getPosition(key: string): ScrollPosition | null {
    const position = this.positions.get(key)
    
    if (!position) {
      return null
    }

    // 检查位置是否过期
    if (Date.now() - position.timestamp > POSITION_EXPIRY) {
      this.positions.delete(key)
      this.saveToStorage()
      return null
    }

    return position
  }

  /**
   * 清除指定页面的滚动位置
   */
  clearPosition(key: string) {
    this.positions.delete(key)
    this.saveToStorage()
  }

  /**
   * 清除所有过期的滚动位置
   */
  clearExpiredPositions() {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.positions.forEach((position, key) => {
      if (now - position.timestamp > POSITION_EXPIRY) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => {
      this.positions.delete(key)
    })

    if (keysToDelete.length > 0) {
      this.saveToStorage()
    }
  }

  /**
   * 从localStorage加载滚动位置
   */
  private loadFromStorage() {
    // 只在客户端环境下执行
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return
    }

    try {
      const stored = localStorage.getItem(SCROLL_STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        this.positions = new Map(Object.entries(data))
        this.clearExpiredPositions()
      }
    } catch (error) {
      console.warn('Failed to load scroll positions from storage:', error)
    }
  }

  /**
   * 保存滚动位置到localStorage
   */
  private saveToStorage() {
    // 只在客户端环境下执行
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return
    }

    try {
      const data = Object.fromEntries(this.positions)
      localStorage.setItem(SCROLL_STORAGE_KEY, JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save scroll positions to storage:', error)
    }
  }
}

// 创建全局实例
export const scrollPositionManager = new ScrollPositionManager()

/**
 * React Hook for managing scroll position
 */
export function useScrollPosition(key: string) {
  const saveCurrentPosition = () => {
    scrollPositionManager.savePosition(key)
  }

  const restorePosition = (smooth: boolean = true) => {
    return scrollPositionManager.restorePosition(key, smooth)
  }

  const clearPosition = () => {
    scrollPositionManager.clearPosition(key)
  }

  return {
    saveCurrentPosition,
    restorePosition,
    clearPosition
  }
}

/**
 * 生成页面滚动位置的key
 */
export function generateScrollKey(pathname: string, searchParams?: URLSearchParams): string {
  let key = pathname
  if (searchParams && searchParams.toString()) {
    key += `?${searchParams.toString()}`
  }
  return key
}
