import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for database
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          avatar_url?: string | null
          updated_at?: string
        }
      }
      architectural_works: {
        Row: {
          id: string
          title: string
          architect: string
          year: number
          award: string
          category: string
          description: string
          images: string[]
          cover_image: string
          likes_count: number
          views_count: number
          location: string | null
          tags: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          architect: string
          year: number
          award: string
          category: string
          description: string
          images?: string[]
          cover_image: string
          likes_count?: number
          views_count?: number
          location?: string | null
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          title?: string
          architect?: string
          year?: number
          award?: string
          category?: string
          description?: string
          images?: string[]
          cover_image?: string
          likes_count?: number
          views_count?: number
          location?: string | null
          tags?: string[]
          updated_at?: string
        }
      }
      likes: {
        Row: {
          id: string
          user_id: string
          work_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          work_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          work_id?: string
          created_at?: string
        }
      }
      favorites: {
        Row: {
          id: string
          user_id: string
          work_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          work_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          work_id?: string
          created_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          work_id: string
          user_id: string
          content: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          work_id: string
          user_id: string
          content: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          work_id?: string
          user_id?: string
          content?: string
          updated_at?: string
        }
      }
    }
  }
}
