// 根据环境动态设置API基础URL
const getApiBaseUrl = () => {
  // 在浏览器环境中，始终使用前端API路由代理
  if (typeof window !== "undefined") {
    return "/api"  // 使用前端API路由代理
  }

  // 服务端渲染环境：容器内部通信
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api"
}

const API_BASE_URL = getApiBaseUrl()

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    // Get token from localStorage if available
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("access_token")
      console.log(`ApiClient initialized - Token: ${this.token ? 'Present' : 'Missing'}`)
    }
  }

  // 动态获取API基础URL（用于处理域名变化）
  private getBaseURL(): string {
    if (typeof window !== "undefined") {
      // 生产环境或非localhost环境：使用前端API路由代理
      if (process.env.NODE_ENV === "production" || window.location.hostname !== "localhost") {
        return "/api"  // 使用前端API路由代理，不直接访问8000端口
      }
      // 开发环境：直接访问后端
      return "http://localhost:8000/api"
    }
    return this.baseURL
  }

  setToken(token: string) {
    this.token = token
    if (typeof window !== "undefined") {
      localStorage.setItem("access_token", token)
    }
    console.log(`Token set: ${token ? 'Present' : 'Missing'}`)
  }

  clearToken() {
    this.token = null
    if (typeof window !== "undefined") {
      localStorage.removeItem("access_token")
    }
    console.log(`Token cleared`)
  }

  // 动态刷新token（用于处理页面刷新后token丢失的问题）
  private refreshToken() {
    if (typeof window !== "undefined") {
      const storedToken = localStorage.getItem("access_token")
      if (storedToken && storedToken !== this.token) {
        this.token = storedToken
        console.log(`Token refreshed from localStorage`)
      }
    }
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    // 尝试刷新token
    this.refreshToken()

    const url = `${this.getBaseURL()}${endpoint}`
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
      console.log(`Frontend API - Auth header: Present`)
    } else {
      console.log(`Frontend API - No auth header found`)
    }

    console.log(`API请求: ${options.method || 'GET'} ${url}`)

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: "Network error" }))
      console.error(`API错误: ${response.status} ${url}`, error)
      throw new Error(error.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Auth endpoints
  async login(email: string, password: string) {
    const formData = new FormData()
    formData.append("username", email)
    formData.append("password", password)

    const url = `${this.getBaseURL()}/auth/login`
    console.log(`登录请求: POST ${url}`)

    const response = await fetch(url, {
      method: "POST",
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: "Login failed" }))
      throw new Error(error.detail)
    }

    const data = await response.json()
    this.setToken(data.access_token)
    return data
  }

  async register(username: string, email: string, password: string) {
    return this.request("/auth/register", {
      method: "POST",
      body: JSON.stringify({ username, email, password }),
    })
  }

  async getCurrentUser() {
    return this.request("/auth/me")
  }

  // Works endpoints
  async getWorks(
    params: {
      year?: number
      award?: string
      search?: string
      featured?: boolean
    } = {},
  ) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    const query = searchParams.toString()
    return this.request(`/works${query ? `?${query}` : ""}`)
  }

  async getWork(id: string) {
    return this.request(`/works/${id}`)
  }

  async getAdminWork(id: string) {
    return this.request(`/admin/works/${id}`)
  }

  async toggleLike(workId: string) {
    return this.request(`/works/${workId}/like`, { method: "POST" })
  }

  async toggleFavorite(workId: string) {
    return this.request(`/works/${workId}/favorite`, { method: "POST" })
  }

  async getComments(workId: string) {
    return this.request(`/works/${workId}/comments`)
  }

  async createComment(workId: string, data: { content: string; parent_id?: number }) {
    return this.request(`/works/${workId}/comments`, {
      method: "POST",
      body: JSON.stringify(data),
    })
  }

  async replyToComment(workId: string, commentId: number, data: { content: string }) {
    return this.request(`/works/${workId}/comments/${commentId}/reply`, {
      method: "POST",
      body: JSON.stringify(data),
    })
  }

  // User endpoints
  async getUserLikedWorks() {
    return this.request("/user/liked-works")
  }

  async getUserFavoriteWorks() {
    return this.request("/user/favorite-works")
  }

  async changePassword(data: { currentPassword: string; newPassword: string }) {
    // 转换字段名以匹配后端API期望的格式
    const requestData = {
      current_password: data.currentPassword,
      new_password: data.newPassword
    }

    return this.request("/user/change-password", {
      method: "POST",
      body: JSON.stringify(requestData),
    })
  }

  async getUserProfile() {
    return this.request("/user/profile")
  }

  async updateUserProfile(data: { username?: string; avatar_url?: string }) {
    return this.request("/user/profile", {
      method: "PUT",
      body: JSON.stringify(data),
    })
  }

  // Admin endpoints
  async getAdminWorks(published?: boolean) {
    const params = published !== undefined ? `?published=${published}` : ""
    return this.request(`/admin/works${params}`)
  }

  async createWork(work: any) {
    return this.request("/admin/works", {
      method: "POST",
      body: JSON.stringify(work),
    })
  }

  async updateWork(id: string, work: any) {
    return this.request(`/admin/works/${id}`, {
      method: "PUT",
      body: JSON.stringify(work),
    })
  }

  async publishWork(id: string) {
    return this.request(`/admin/works/${id}/publish`, { method: "POST" })
  }

  async unpublishWork(id: string) {
    return this.request(`/admin/works/${id}/unpublish`, { method: "POST" })
  }

  async toggleFeatured(id: string) {
    return this.request(`/admin/works/${id}/feature`, { method: "POST" })
  }

  async deleteWork(id: string) {
    return this.request(`/admin/works/${id}`, { method: "DELETE" })
  }

  // Admin user management
  async getAdminUsers(params: {
    skip?: number
    limit?: number
    search?: string
    is_admin?: boolean
    is_active?: boolean
  } = {}) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    const query = searchParams.toString()
    return this.request(`/admin/users${query ? `?${query}` : ""}`)
  }

  async getAdminUser(id: string) {
    return this.request(`/admin/users/${id}`)
  }

  async createAdminUser(data: {
    username: string
    email: string
    password: string
    is_admin?: boolean
  }) {
    return this.request("/admin/users", {
      method: "POST",
      body: JSON.stringify(data),
    })
  }

  async updateAdminUser(id: string, data: {
    username?: string
    email?: string
    is_active?: boolean
    is_admin?: boolean
    avatar_url?: string
  }) {
    return this.request(`/admin/users/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  }

  async deleteAdminUser(id: string) {
    return this.request(`/admin/users/${id}`, { method: "DELETE" })
  }

  async toggleUserAdmin(id: string) {
    return this.request(`/admin/users/${id}/toggle-admin`, { method: "POST" })
  }

  async toggleUserActive(id: string) {
    return this.request(`/admin/users/${id}/toggle-active`, { method: "POST" })
  }

  // Admin comments management
  async getAdminComments(params: {
    skip?: number
    limit?: number
    approved?: boolean
  } = {}) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    const query = searchParams.toString()
    return this.request(`/admin/comments${query ? `?${query}` : ""}`)
  }

  async approveComment(id: string) {
    return this.request(`/admin/comments/${id}/approve`, { method: "POST" })
  }

  async rejectComment(id: string) {
    return this.request(`/admin/comments/${id}/reject`, { method: "POST" })
  }

  async deleteAdminComment(id: string) {
    return this.request(`/admin/comments/${id}`, { method: "DELETE" })
  }

  // Admin stats
  async getAdminStats() {
    return this.request("/admin/stats")
  }
}

export const apiClient = new ApiClient(API_BASE_URL)
