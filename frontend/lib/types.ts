export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  createdAt: Date
}

export interface ArchitecturalWork {
  id: string
  title: string
  architect: string
  year: number
  award: AwardLevel
  category: string
  description: string
  images: string[]
  coverImage: string
  likes: number
  views: number
  createdAt: Date
  tags: string[]
  location?: string
  isLikedByUser?: boolean
  isFavoriteByUser?: boolean
}

// API response type that matches backend
export interface Work {
  id: number
  title: string
  architect: string
  year: number
  award: string
  description: string
  cover_image: string
  images: string[]
  location?: string
  tags: string[]
  is_published: boolean
  is_featured: boolean
  likes_count: number
  views_count: number
  creator_id?: number
  created_at: string
  updated_at?: string
  published_at?: string
  is_liked_by_user?: boolean
  is_favorite_by_user?: boolean
}

export interface Comment {
  id: number
  work_id: number
  user_id: number
  username: string
  user_avatar?: string
  content: string
  created_at: string
  is_approved: boolean
  parent_id?: number
  replies?: Comment[]
  reply_to_username?: string
}

export interface Favorite {
  id: string
  userId: string
  workId: string
  createdAt: Date
}

export type AwardLevel = "特等奖" | "一等奖" | "二等奖" | "三等奖" | "优秀奖" | "入围奖"

export interface Category {
  id: string
  label: string
  icon: string
}

export interface YearFilter {
  year: number
  count: number
}
