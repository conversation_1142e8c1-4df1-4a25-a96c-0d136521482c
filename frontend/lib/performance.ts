/**
 * 前端性能优化配置和工具函数
 */

// 图片优化配置
export const IMAGE_CONFIG = {
  // 图片尺寸配置
  sizes: {
    thumbnail: { width: 300, quality: 75 },
    medium: { width: 800, quality: 80 },
    large: { width: 1200, quality: 85 },
    original: { width: null, quality: 90 }
  },
  
  // 响应式图片sizes属性
  responsiveSizes: {
    card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    hero: '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw',
    gallery: '(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw',
    detail: '(max-width: 768px) 100vw, 80vw'
  },
  
  // 预加载配置
  preload: {
    priority: ['hero', 'above-fold'],
    lazy: ['below-fold', 'gallery'],
    eager: ['critical']
  }
};

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // Core Web Vitals阈值
  thresholds: {
    lcp: { good: 2500, poor: 4000 },
    fid: { good: 100, poor: 300 },
    cls: { good: 0.1, poor: 0.25 }
  },
  
  // 监控间隔
  intervals: {
    metrics: 5000,
    memory: 10000,
    network: 30000
  }
};

// 缓存配置
export const CACHE_CONFIG = {
  // 图片缓存时间（秒）
  images: 86400 * 30, // 30天
  
  // API缓存时间
  api: {
    works: 300,      // 5分钟
    categories: 3600, // 1小时
    static: 86400    // 1天
  },
  
  // 本地存储键名
  keys: {
    imageCache: 'image_cache',
    apiCache: 'api_cache',
    userPrefs: 'user_preferences'
  }
};

/**
 * 获取优化后的图片URL
 */
export function getOptimizedImageUrl(
  originalUrl: string, 
  size: keyof typeof IMAGE_CONFIG.sizes = 'medium'
): string {
  if (!originalUrl || originalUrl.startsWith('http')) {
    return originalUrl;
  }

  // 检查是否是作品图片
  if (originalUrl.includes('/uploads/works/')) {
    const fileName = originalUrl.split('/').pop();
    if (fileName) {
      const baseName = fileName.replace(/\.[^/.]+$/, '');
      return `/uploads/works_optimized/${baseName}_${size}.webp`;
    }
  }

  return originalUrl;
}

/**
 * 检测WebP支持
 */
export function checkWebPSupport(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * 预加载关键资源
 */
export function preloadCriticalResources(urls: string[]) {
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
}

/**
 * 延迟加载非关键资源
 */
export function lazyLoadResources(callback: () => void, delay: number = 100) {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(callback, { timeout: delay });
  } else {
    setTimeout(callback, delay);
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 图片预加载器
 */
export class ImagePreloader {
  private cache = new Map<string, HTMLImageElement>();
  private loading = new Set<string>();

  async preload(url: string): Promise<HTMLImageElement> {
    if (this.cache.has(url)) {
      return this.cache.get(url)!;
    }

    if (this.loading.has(url)) {
      return new Promise((resolve, reject) => {
        const checkLoaded = () => {
          if (this.cache.has(url)) {
            resolve(this.cache.get(url)!);
          } else if (!this.loading.has(url)) {
            reject(new Error('Failed to load image'));
          } else {
            setTimeout(checkLoaded, 50);
          }
        };
        checkLoaded();
      });
    }

    this.loading.add(url);

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.cache.set(url, img);
        this.loading.delete(url);
        resolve(img);
      };
      img.onerror = () => {
        this.loading.delete(url);
        reject(new Error(`Failed to load image: ${url}`));
      };
      img.src = url;
    });
  }

  preloadBatch(urls: string[]): Promise<HTMLImageElement[]> {
    return Promise.allSettled(urls.map(url => this.preload(url)))
      .then(results => 
        results
          .filter((result): result is PromiseFulfilledResult<HTMLImageElement> => 
            result.status === 'fulfilled'
          )
          .map(result => result.value)
      );
  }

  clear() {
    this.cache.clear();
    this.loading.clear();
  }

  getStats() {
    return {
      cached: this.cache.size,
      loading: this.loading.size
    };
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private observers: PerformanceObserver[] = [];
  private metrics = new Map<string, number>();

  start() {
    this.observeLCP();
    this.observeFID();
    this.observeCLS();
  }

  stop() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  private observeLCP() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.set('lcp', lastEntry.startTime);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(observer);
      } catch (e) {
        console.warn('LCP observer not supported');
      }
    }
  }

  private observeFID() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const fid = (entry as any).processingStart - entry.startTime;
            this.metrics.set('fid', fid);
          }
        });
        observer.observe({ entryTypes: ['first-input'] });
        this.observers.push(observer);
      } catch (e) {
        console.warn('FID observer not supported');
      }
    }
  }

  private observeCLS() {
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          this.metrics.set('cls', clsValue);
        });
        observer.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(observer);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }
  }

  getMetrics() {
    return Object.fromEntries(this.metrics);
  }

  getGrade(metric: string): 'good' | 'needs-improvement' | 'poor' {
    const value = this.metrics.get(metric) || 0;
    const thresholds = PERFORMANCE_CONFIG.thresholds[metric as keyof typeof PERFORMANCE_CONFIG.thresholds];
    
    if (!thresholds) return 'good';
    
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.poor) return 'needs-improvement';
    return 'poor';
  }
}

// 全局实例
export const imagePreloader = new ImagePreloader();
export const performanceMonitor = new PerformanceMonitor();
