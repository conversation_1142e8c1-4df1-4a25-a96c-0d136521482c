#!/bin/bash

# Nginx 配置测试脚本
# 用于验证反向代理配置是否正确

echo "🔧 Nginx 配置测试脚本"
echo "======================"

# 检查服务状态
echo "📊 检查容器服务状态..."
if command -v docker &> /dev/null; then
    if docker ps | grep -q hypcup-event; then
        echo "✅ 容器正在运行"
        
        # 检查端口
        echo "🔍 检查端口状态..."
        if netstat -tuln 2>/dev/null | grep -q ":3000"; then
            echo "✅ 前端端口 3000 正在监听"
        else
            echo "❌ 前端端口 3000 未监听"
        fi
        
        if netstat -tuln 2>/dev/null | grep -q ":8000"; then
            echo "✅ 后端端口 8000 正在监听"
        else
            echo "❌ 后端端口 8000 未监听"
        fi
    else
        echo "❌ 容器未运行"
        exit 1
    fi
else
    echo "⚠️  Docker 命令不可用，跳过容器检查"
fi

echo ""
echo "🌐 测试本地服务连接..."

# 测试前端服务
echo "测试前端服务 (localhost:3000):"
if curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:3000/ 2>/dev/null; then
    echo "✅ 前端服务响应正常"
else
    echo "❌ 前端服务无响应"
fi

# 测试后端服务
echo "测试后端服务 (localhost:8000):"
if curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:8000/ 2>/dev/null; then
    echo "✅ 后端服务响应正常"
else
    echo "❌ 后端服务无响应"
fi

# 测试后端健康检查
echo "测试后端健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 健康检查响应: $health_response"
else
    echo "❌ 健康检查失败"
fi

echo ""
echo "🔍 测试 API 端点..."

# 测试作品列表 API
echo "测试作品列表 API:"
works_response=$(curl -s -w "HTTP状态码: %{http_code}" http://localhost:8000/api/works/ 2>/dev/null)
if echo "$works_response" | grep -q "200"; then
    echo "✅ 作品列表 API 正常"
else
    echo "❌ 作品列表 API 异常: $works_response"
fi

# 测试前端 API 代理
echo "测试前端 API 代理:"
proxy_response=$(curl -s -w "HTTP状态码: %{http_code}" http://localhost:3000/api/works 2>/dev/null)
if echo "$proxy_response" | grep -q "200"; then
    echo "✅ 前端 API 代理正常"
else
    echo "❌ 前端 API 代理异常: $proxy_response"
fi

echo ""
echo "📋 Nginx 配置建议..."

echo "1. 确保 Nginx 配置文件包含以下关键配置:"
echo "   - location /api/ { proxy_pass http://127.0.0.1:8000/api/; }"
echo "   - location / { proxy_pass http://127.0.0.1:3000/; }"

echo ""
echo "2. 检查 Nginx 配置语法:"
echo "   nginx -t"

echo ""
echo "3. 重新加载 Nginx 配置:"
echo "   nginx -s reload"

echo ""
echo "4. 查看 Nginx 错误日志:"
echo "   tail -f /var/log/nginx/error.log"

echo ""
echo "📝 推荐的 Nginx 配置文件已生成:"
echo "   - nginx-simple.conf (简化版本)"
echo "   - nginx.conf (完整版本)"

echo ""
echo "🚀 修复步骤:"
echo "1. 复制正确的配置文件到 Nginx"
echo "2. 测试配置: nginx -t"
echo "3. 重新加载: nginx -s reload"
echo "4. 测试访问: curl -I https://comp.uedmagazine.net/"

echo ""
echo "🔧 如果问题仍然存在:"
echo "1. 检查防火墙设置"
echo "2. 确认端口映射正确"
echo "3. 查看详细的 Nginx 日志"
echo "4. 验证 SSL 证书配置"
