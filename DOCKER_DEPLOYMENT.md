# Hypcup Event Platform - Docker 容器化部署指南

## 📋 概述

本项目采用Docker容器化部署，将前端（Next.js）和后端（FastAPI）服务打包在一个容器中，实现简化的部署和管理。

## 🏗️ 架构设计

```
Docker容器 (hypcup-event-app)
├── 前端服务 (Next.js) - 端口 3000
├── 后端服务 (FastAPI) - 端口 8000
├── 数据库 (SQLite) - 文件映射
└── 文件上传 - 目录映射
```

## 📁 项目结构

```
hypcup/
├── Dockerfile                 # 容器构建文件
├── docker-compose.yml        # 容器编排配置
├── simple-run.sh            # 快速启动脚本
├── .dockerignore            # Docker构建忽略文件
├── .env.example             # 环境变量示例
├── data/                    # 数据持久化目录
│   ├── app.db              # SQLite数据库文件
│   └── log/                # 日志文件目录
│       ├── backend.out.log  # 后端输出日志
│       ├── backend.err.log  # 后端错误日志
│       ├── frontend.out.log # 前端输出日志
│       └── frontend.err.log # 前端错误日志
├── uploads/                 # 文件上传目录
├── frontend/               # 前端源码
└── backend/                # 后端源码
```

## 🚀 快速启动

### 1. 准备工作

确保已安装Docker和Docker Compose：

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 确保基础镜像存在
docker images | grep hypcup-event-base
```

### 2. 一键启动

```bash
# 给启动脚本执行权限
chmod +x simple-run.sh

# 启动服务
./simple-run.sh
```

### 3. 手动启动

```bash
# 创建数据目录
mkdir -p data/log uploads

# 构建并启动容器
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🔧 配置说明

### 环境变量

主要环境变量在 `docker-compose.yml` 中配置：

```yaml
environment:
  - NODE_ENV=production
  - DATABASE_URL=sqlite:////opt/hypcup-event/data/app.db
  - NEXT_PUBLIC_API_URL=http://localhost:8000/api
  - SECRET_KEY=ZaNTW3rPyFWnMhptPQEv8CmGtbJhMPVe8x2Q
  - ADMIN_EMAIL=<EMAIL>
  - ADMIN_PASSWORD=123456
```

### 端口映射

- **3000**: 前端服务 (Next.js)
- **8000**: 后端API服务 (FastAPI)

### 数据持久化

- `./data:/opt/hypcup-event/data` - 数据库和日志文件
- `./uploads:/opt/hypcup-event/uploads` - 用户上传文件

## 📊 服务访问

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 🛠️ 常用命令

### 容器管理

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up --build -d

# 查看容器状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f hypcup-event
```

### 容器内操作

```bash
# 进入容器
docker-compose exec hypcup-event bash

# 查看容器内进程
docker-compose exec hypcup-event ps aux

# 查看环境变量
docker-compose exec hypcup-event env
```

### 数据库操作

```bash
# 进入容器并访问数据库
docker-compose exec hypcup-event bash
cd /opt/hypcup-event/backend
python -c "
from app.core.database import SessionLocal
from app.models.user import User
db = SessionLocal()
users = db.query(User).all()
print(f'用户数量: {len(users)}')
"
```

## 🔍 故障排除

### 1. 容器启动失败

```bash
# 查看详细日志
docker-compose logs

# 检查容器状态
docker-compose ps

# 重新构建
docker-compose build --no-cache
```

### 2. 端口冲突

如果端口被占用，修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "3001:3000"  # 前端改为3001
  - "8001:8000"  # 后端改为8001
```

### 3. 数据库连接问题

检查数据库文件权限和路径：

```bash
# 检查数据目录
ls -la data/

# 检查数据库文件
ls -la data/app.db

# 重新创建数据库
rm data/app.db
docker-compose restart
```

### 4. 前端无法访问后端

检查API地址配置：

```bash
# 查看环境变量
docker-compose exec hypcup-event env | grep API

# 测试后端连接
docker-compose exec hypcup-event curl http://localhost:8000/health
```

## 📈 性能优化

### 1. 构建优化

- 使用 `.dockerignore` 减少构建上下文
- 多阶段构建减少镜像大小
- 缓存依赖安装层

### 2. 运行时优化

- 设置合适的内存限制
- 配置健康检查
- 使用生产环境配置

### 3. 监控和日志

```bash
# 查看容器资源使用
docker stats hypcup-event-app

# 查看日志文件大小
du -sh data/log/

# 清理旧日志
find data/log/ -name "*.log" -mtime +7 -delete
```

## 🔒 安全建议

1. **修改默认密码**: 更改 `ADMIN_PASSWORD` 和 `SECRET_KEY`
2. **使用HTTPS**: 在生产环境配置SSL证书
3. **限制访问**: 配置防火墙规则
4. **定期备份**: 备份数据库和上传文件
5. **更新依赖**: 定期更新基础镜像和依赖包

## 📝 维护指南

### 定期维护任务

```bash
# 清理Docker缓存
docker system prune -f

# 备份数据
tar -czf backup-$(date +%Y%m%d).tar.gz data/ uploads/

# 更新镜像
docker-compose pull
docker-compose up -d
```

### 日志管理

```bash
# 查看日志大小
du -sh data/log/

# 轮转日志（可配置logrotate）
mv data/log/backend.out.log data/log/backend.out.log.old
docker-compose restart
```

## 🎯 下一步

1. 配置反向代理（Nginx/OpenResty）
2. 设置SSL证书
3. 配置监控和告警
4. 实施自动化部署
5. 配置数据库备份策略
