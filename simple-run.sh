#!/bin/bash

# Hypcup Event Platform 容器启动脚本
# 使用Docker Compose启动前后端服务

set -e

echo "🚀 启动 Hypcup Event Platform..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查基础镜像是否存在
if ! docker images | grep -q "hypcup-event-base"; then
    echo "❌ 基础镜像 hypcup-event-base:latest 不存在"
    echo "请先构建或拉取基础镜像"
    exit 1
fi

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p data/log
mkdir -p uploads

# 设置目录权限
chmod 755 data uploads
chmod 755 data/log

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "📊 服务信息："
    echo "  前端地址: http://localhost:3000"
    echo "  后端API: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
    echo ""
    echo "📝 查看日志："
    echo "  docker-compose logs -f"
    echo ""
    echo "🛑 停止服务："
    echo "  docker-compose down"
else
    echo "❌ 服务启动失败，请检查日志："
    echo "  docker-compose logs"
    exit 1
fi
