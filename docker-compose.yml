version: '3.8'

services:
  hypcup-event:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hypcup-event-app
    restart: unless-stopped
    ports:
      - "3000:3000"  # 前端端口
      - "8000:8000"  # 后端API端口
    volumes:
      # 数据库文件持久化
      - ./data:/opt/hypcup-event/data
      # 上传文件持久化
      - ./uploads:/opt/hypcup-event/uploads
      # 日志文件持久化（可选，因为已经映射了data目录）
      - ./data/log:/opt/hypcup-event/data/log
    environment:
      # 基础配置
      - NODE_ENV=production
      - PORT=3000
      
      # 数据库配置
      - DATABASE_URL=sqlite:////opt/hypcup-event/data/app.db
      - UPLOAD_DIR=/opt/hypcup-event/uploads
      
      # API配置
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api
      
      # 应用安全配置
      - SECRET_KEY=ZaNTW3rPyFWnMhptPQEv8CmGtbJhMPVe8x2Q
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=123456
      
      # 前端构建配置
      - PACKAGE_MANAGER=pnpm
      - CONTAINER_PACKAGE_URL=https://registry.npmmirror.com/
    networks:
      - hypcup-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  hypcup-network:
    driver: bridge

volumes:
  # 如果需要命名卷，可以在这里定义
  hypcup-data:
    driver: local
  hypcup-uploads:
    driver: local
