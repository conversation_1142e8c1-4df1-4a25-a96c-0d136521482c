# 使用本地的基础镜像（包含Node.js和Python）
FROM hypcup-event-base:latest

# 设置工作目录
WORKDIR /opt/hypcup-event

# 设置环境变量
ENV NODE_ENV=production
ENV PYTHONPATH=/opt/hypcup-event/backend
ENV PYTHONUNBUFFERED=1

# 创建必要的目录
RUN mkdir -p /opt/hypcup-event/data/log \
    && mkdir -p /opt/hypcup-event/uploads \
    && mkdir -p /opt/hypcup-event/frontend \
    && mkdir -p /opt/hypcup-event/backend

# 复制后端代码和依赖文件
COPY backend/ /opt/hypcup-event/backend/
COPY backend/requirements.txt /opt/hypcup-event/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制前端代码
COPY frontend/ /opt/hypcup-event/frontend/

# 设置前端工作目录并安装依赖
WORKDIR /opt/hypcup-event/frontend
RUN npm install -g pnpm \
    && pnpm install \
    && pnpm build

# 回到主工作目录
WORKDIR /opt/hypcup-event

# 创建统一环境变量配置文件
RUN cat > /env-unified.sh << 'EOF'
#!/bin/bash
# 基础配置
export NODE_ENV="production"
export PORT="3000"

# 数据库配置 (绝对路径)
export DATABASE_URL="sqlite:////opt/hypcup-event/data/app.db"
export UPLOAD_DIR="/opt/hypcup-event/uploads"

# API配置
export NEXT_PUBLIC_API_URL="http://localhost:8000/api"

# 应用安全配置
export SECRET_KEY="ZaNTW3rPyFWnMhptPQEv8CmGtbJhMPVe8x2Q"
export ADMIN_EMAIL="<EMAIL>"
export ADMIN_PASSWORD="123456"

# 前端构建配置
export PACKAGE_MANAGER="pnpm"
export CONTAINER_PACKAGE_URL="https://registry.npmmirror.com/"
EOF

# 创建后端启动脚本
RUN cat > /start-backend.sh << 'EOF'
#!/bin/bash
source /env-unified.sh

echo "Starting backend server..."
cd /opt/hypcup-event/backend

# 创建数据库表
python -c "
from app.core.database import engine
from app.models import user, work
user.Base.metadata.create_all(bind=engine)
work.Base.metadata.create_all(bind=engine)
print('Database tables created successfully')
"

# 启动FastAPI服务器
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

# 创建前端启动脚本
RUN cat > /start-frontend.sh << 'EOF'
#!/bin/bash
source /env-unified.sh

echo "Starting frontend server..."
cd /opt/hypcup-event/frontend

# 启动Next.js服务器
exec pnpm start
EOF

# 创建主启动脚本
RUN cat > /start-services.sh << 'EOF'
#!/bin/bash
source /env-unified.sh

echo "Starting Hypcup Event Platform..."

# 确保数据目录存在
mkdir -p /opt/hypcup-event/data/log
mkdir -p /opt/hypcup-event/uploads

# 启动后端服务（后台运行）
echo "Starting backend service..."
/start-backend.sh > /opt/hypcup-event/data/log/backend.out.log 2> /opt/hypcup-event/data/log/backend.err.log &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端服务（前台运行）
echo "Starting frontend service..."
exec /start-frontend.sh > /opt/hypcup-event/data/log/frontend.out.log 2> /opt/hypcup-event/data/log/frontend.err.log
EOF

# 设置脚本执行权限
RUN chmod +x /env-unified.sh /start-backend.sh /start-frontend.sh /start-services.sh

# 暴露端口
EXPOSE 3000 8000

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health && curl -f http://localhost:3000 || exit 1

# 启动服务
CMD ["/start-services.sh"]
