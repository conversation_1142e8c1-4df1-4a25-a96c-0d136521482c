这是一个建筑作品展示平台，采用前后端分离的现代Web架构：
技术栈
前端: Next.js 15.2.4 (React框架)
后端: FastAPI (Python)
数据库: SQLite
部署: Docker + Supervisor
反向代理: OpenResty/Nginx
包管理: pnpm (前端) + pip (后端)


用户请求 → OpenResty/Nginx → Docker容器 → 前端(3000) + 后端(8000)
                ↓
            反向代理配置
                ↓
         Next.js应用 ←→ FastAPI应用 ←→ SQLite数据库
		 
		 
hypcup/
├──  frontend/                 # Next.js前端应用
├──  backend/                  # FastAPI后端应用
├──  data/                     # 数据存储目录
├──  docker-compose.yml       # Docker编排配置
├──  simple-run.sh           # 容器启动脚本
└──  .env                     # 环境变量文件

frontend/
├──  app/                      # Next.js 13+ App Router
│   ├──  admin/               # 管理员后台
│   │   ├──  comments/        # 评论管理
│   │   │   └── page.tsx        # 评论管理页面
│   │   ├──  users/           # 用户管理
│   │   ├──  works/           # 作品管理
│   │   └── layout.tsx          # 管理后台布局
│   ├──  api/                 # API路由 (Next.js API)
│   │   ├──  auth/            # 认证相关API
│   │   ├──  admin/           # 管理员API代理
│   │   └──  works/           # 作品相关API代理
│   ├──  work/                # 作品详情页
│   │   └── [id]/               # 动态路由
│   ├──  login/               # 登录页面
│   ├──  register/            # 注册页面
│   ├── layout.tsx              # 根布局
│   ├── page.tsx                # 首页
│   └── globals.css             # 全局样式
├──  components/              # React组件
│   ├──  admin/               # 管理后台组件
│   ├──  ui/                  # UI基础组件
│   ├──  work/                # 作品相关组件
│   └──  各种组件文件
├──  lib/                     # 工具库
│   ├── api.ts                  # API客户端
│   ├── auth.ts                 # 认证逻辑
│   └── utils.ts                # 工具函数
├──  hooks/                   # React Hooks
├──  styles/                  # 样式文件
├──  public/                  # 静态资源
├──  package.json             # 依赖配置
├──  next.config.mjs          # Next.js配置
├──  tailwind.config.ts       # Tailwind CSS配置
└──  tsconfig.json            # TypeScript配置

backend/
├──  app/                     # FastAPI应用
│   ├──  api/                 # API路由
│   │   ├── admin.py            # 管理员API
│   │   ├── auth.py             # 认证API
│   │   ├── works.py            # 作品API
│   │   └── users.py            # 用户API
│   ├──  core/                # 核心配置
│   │   ├── config.py           # 应用配置
│   │   ├── database.py         # 数据库配置
│   │   └── security.py         # 安全配置
│   ├──  models/              # 数据模型
│   │   ├── user.py             # 用户模型
│   │   ├── work.py             # 作品模型 (包含Comment)
│   │   └── __init__.py
│   ├──  schemas/             # Pydantic模式
│   │   ├── user.py             # 用户模式
│   │   ├── work.py             # 作品模式
│   │   └── auth.py             # 认证模式
│   ├──  utils/               # 工具函数
│   └── main.py                 # FastAPI应用入口
├──  requirements.txt         # Python依赖
└──  alembic.ini             # 数据库迁移配置


data/
├──  app.db                  # SQLite数据库文件
└──  log/                    # 日志目录
    ├── backend.out.log        # 后端输出日志
    ├── backend.err.log        # 后端错误日志
    ├── frontend.out.log       # 前端输出日志
    └── frontend.err.log       # 前端错误日志
	
	
#容器化部署
Docker配置
基础镜像: hypcup-event-base:latest
端口映射: 3000(前端) + 8000(后端)
数据持久化: 数据库、上传文件、日志
