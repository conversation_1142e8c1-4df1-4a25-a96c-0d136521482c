from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class WorkBase(BaseModel):
    title: str
    architect: str
    year: int
    award: str

    description: str
    location: Optional[str] = None
    tags: List[str] = []

class WorkCreate(WorkBase):
    cover_image: str
    images: List[str] = []

class WorkUpdate(BaseModel):
    title: Optional[str] = None
    architect: Optional[str] = None
    year: Optional[int] = None
    award: Optional[str] = None

    description: Optional[str] = None
    location: Optional[str] = None
    tags: Optional[List[str]] = None
    cover_image: Optional[str] = None
    images: Optional[List[str]] = None
    is_published: Optional[bool] = None
    is_featured: Optional[bool] = None

class WorkInDB(WorkBase):
    id: int
    cover_image: str
    images: List[str]
    is_published: bool
    is_featured: bool
    likes_count: int
    views_count: int
    creator_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    published_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class Work(WorkInDB):
    is_liked_by_user: bool = False
    is_favorite_by_user: bool = False

class WorkAdmin(WorkInDB):
    pass

class CommentBase(BaseModel):
    content: str

class CommentCreate(CommentBase):
    parent_id: Optional[int] = None

class Comment(CommentBase):
    id: int
    user_id: int
    work_id: int
    parent_id: Optional[int] = None
    username: str
    user_avatar: Optional[str]
    reply_to_username: Optional[str] = None
    is_approved: bool
    created_at: datetime
    replies: Optional[List['Comment']] = []

    class Config:
        from_attributes = True

# 更新前向引用
Comment.model_rebuild()

class CommentAdmin(CommentBase):
    id: int
    user_id: int
    work_id: int
    username: str
    user_avatar: Optional[str]
    is_approved: bool
    created_at: datetime
    work_title: Optional[str] = None

    class Config:
        from_attributes = True
