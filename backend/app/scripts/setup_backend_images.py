import os
import shutil
from pathlib import Path

def setup_backend_images():
    """Copy sample images from frontend to backend uploads directory"""
    
    # Source directory (frontend)
    frontend_samples = Path("public/uploads/works/samples")
    
    # Destination directory (backend)
    backend_uploads = Path("backend/uploads/works/samples")
    backend_uploads.mkdir(parents=True, exist_ok=True)
    
    if not frontend_samples.exists():
        print("Frontend samples directory not found!")
        return
    
    # Copy all sample images
    for image_file in frontend_samples.glob("*.jpg"):
        dest_file = backend_uploads / image_file.name
        if not dest_file.exists():
            shutil.copy2(image_file, dest_file)
            print(f"Copied {image_file.name} to backend uploads")
        else:
            print(f"File {image_file.name} already exists in backend uploads")
    
    print("Backend image setup completed!")

if __name__ == "__main__":
    setup_backend_images()
