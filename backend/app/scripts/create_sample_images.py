import os
import requests
from pathlib import Path

def create_sample_images():
    """Download sample architectural images for seed data"""
    
    # Create samples directory
    samples_dir = Path("public/uploads/works/samples")
    samples_dir.mkdir(parents=True, exist_ok=True)
    
    # Sample images to download
    sample_images = [
        {
            "url": "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop",
            "filename": "residential-1.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop",
            "filename": "residential-2.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1554469384-e58fac16e23a?w=800&h=600&fit=crop",
            "filename": "residential-3.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1577495508048-b635879837f1?w=800&h=600&fit=crop",
            "filename": "cultural-1.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=800&h=600&fit=crop",
            "filename": "cultural-2.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=800&h=600&fit=crop",
            "filename": "educational-1.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop",
            "filename": "commercial-1.jpg"
        },
        {
            "url": "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop",
            "filename": "commercial-2.jpg"
        }
    ]
    
    for image_info in sample_images:
        file_path = samples_dir / image_info["filename"]
        
        # Skip if file already exists
        if file_path.exists():
            print(f"File {image_info['filename']} already exists, skipping...")
            continue
            
        try:
            print(f"Downloading {image_info['filename']}...")
            response = requests.get(image_info["url"], timeout=30)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
                
            print(f"Successfully downloaded {image_info['filename']}")
            
        except Exception as e:
            print(f"Error downloading {image_info['filename']}: {e}")
            # Create a placeholder file instead
            with open(file_path, 'w') as f:
                f.write(f"Placeholder for {image_info['filename']}")
    
    print("Sample images creation completed!")

if __name__ == "__main__":
    create_sample_images()
