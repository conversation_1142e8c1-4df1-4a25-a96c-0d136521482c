import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def check_users():
    """检查数据库中的用户"""
    db = SessionLocal()
    
    try:
        users = db.query(User).all()
        
        print(f"数据库中共有 {len(users)} 个用户:")
        print("-" * 50)
        
        for user in users:
            print(f"ID: {user.id}")
            print(f"邮箱: {user.email}")
            print(f"用户名: {user.username}")
            print(f"是否激活: {user.is_active}")
            print(f"是否为管理员: {user.is_admin}")
            print(f"创建时间: {user.created_at}")
            print("-" * 50)
            
    except Exception as e:
        print(f"查询用户失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_users()
