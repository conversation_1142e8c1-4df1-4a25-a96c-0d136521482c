import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment
from datetime import datetime

def seed_works():
    db = SessionLocal()
    
    # Check if works already exist
    existing_works = db.query(ArchitecturalWork).count()
    if existing_works > 0:
        print("Works already exist")
        return
    
    sample_works = [
        {
            "title": "未来城市住宅综合体",
            "architect": "张建筑工作室",
            "year": 2024,
            "award": "一等奖",
            "category": "residential",
            "description": "这是一个融合了可持续发展理念的现代住宅设计，采用了创新的绿色建筑技术和智能化系统。设计注重居住者的生活品质，通过合理的空间布局和先进的环保技术，创造了一个既舒适又环保的居住环境。",
            "cover_image": "/uploads/works/samples/residential-1.jpg",
            "images": [
                "/uploads/works/samples/residential-1.jpg",
                "/uploads/works/samples/residential-2.jpg",
                "/uploads/works/samples/residential-3.jpg"
            ],
            "location": "北京市朝阳区",
            "tags": ["可持续", "智能化", "现代主义"],
            "is_published": True,
            "is_featured": True,
            "published_at": datetime.utcnow()
        },
        {
            "title": "文化艺术中心设计方案",
            "architect": "李设计事务所",
            "year": 2024,
            "award": "特等奖",
            "category": "cultural",
            "description": "融合传统文化元素与现代建筑语言的文化建筑设计，体现了东西方建筑文化的完美结合。建筑外观采用了传统的坡屋顶和现代的玻璃幕墙相结合，内部空间灵活多变，能够满足各种文化活动的需求。",
            "cover_image": "/uploads/works/samples/cultural-1.jpg",
            "images": [
                "/uploads/works/samples/cultural-1.jpg",
                "/uploads/works/samples/cultural-2.jpg"
            ],
            "location": "上海市黄浦区",
            "tags": ["传统", "现代", "文化"],
            "is_published": True,
            "is_featured": True,
            "published_at": datetime.utcnow()
        },
        {
            "title": "绿色校园教学楼",
            "architect": "王建筑设计院",
            "year": 2023,
            "award": "二等奖",
            "category": "educational",
            "description": "注重环保和学生体验的教育建筑设计，创造了良好的学习环境和社交空间。建筑采用了大量的自然采光和通风设计，减少了能源消耗。同时，设置了多个开放式的学习空间和休息区域，促进学生之间的交流与合作。",
            "cover_image": "/uploads/works/samples/educational-1.jpg",
            "images": ["/uploads/works/samples/educational-1.jpg"],
            "location": "广州市天河区",
            "tags": ["绿色建筑", "教育", "环保"],
            "is_published": True,
            "published_at": datetime.utcnow()
        },
        {
            "title": "现代商业综合体",
            "architect": "陈建筑事务所",
            "year": 2023,
            "award": "三等奖",
            "category": "commercial",
            "description": "集购物、办公、娱乐于一体的现代商业建筑，注重功能性和美观性的平衡。建筑立面采用了动态的LED灯光系统，能够根据不同时间和活动需求变换色彩，成为城市夜景的亮点。",
            "cover_image": "/uploads/works/samples/commercial-1.jpg",
            "images": [
                "/uploads/works/samples/commercial-1.jpg",
                "/uploads/works/samples/commercial-2.jpg"
            ],
            "location": "深圳市南山区",
            "tags": ["商业", "现代", "多功能"],
            "is_published": True,
            "published_at": datetime.utcnow()
        }
    ]
    
    for work_data in sample_works:
        work = ArchitecturalWork(**work_data)
        db.add(work)
    
    db.commit()
    print(f"Created {len(sample_works)} sample works")
    db.close()

if __name__ == "__main__":
    seed_works()
