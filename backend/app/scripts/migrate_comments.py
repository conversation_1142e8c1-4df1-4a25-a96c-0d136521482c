import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.database import SessionLocal, engine
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def migrate_comments():
    """为评论表添加审核状态字段"""
    db = SessionLocal()
    
    try:
        # 检查是否已经有 is_approved 字段 (SQLite)
        result = db.execute(text("PRAGMA table_info(comments)"))
        columns = [row[1] for row in result.fetchall()]

        if 'is_approved' in columns:
            print("is_approved 字段已存在，跳过迁移")
            return

        # 添加 is_approved 字段
        print("添加 is_approved 字段到 comments 表...")
        db.execute(text("ALTER TABLE comments ADD COLUMN is_approved BOOLEAN DEFAULT FALSE"))

        # 将现有评论设置为已审核（向后兼容）
        print("将现有评论设置为已审核...")
        db.execute(text("UPDATE comments SET is_approved = 1 WHERE is_approved IS NULL OR is_approved = 0"))

        db.commit()
        print("评论表迁移完成！")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    migrate_comments()
