#!/usr/bin/env python3
"""
数据库迁移脚本：为comments表添加parent_id字段以支持嵌套回复
"""

import sqlite3
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def migrate_database():
    """执行数据库迁移"""
    
    # 数据库文件路径
    db_paths = [
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db'),
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'architectural_works.db')
    ]
    
    for db_path in db_paths:
        if not os.path.exists(db_path):
            print(f"数据库文件不存在: {db_path}")
            continue
            
        print(f"正在迁移数据库: {db_path}")
        
        try:
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查是否已经存在parent_id字段
            cursor.execute("PRAGMA table_info(comments)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'parent_id' in columns:
                print(f"  parent_id字段已存在，跳过迁移")
                conn.close()
                continue
            
            print(f"  添加parent_id字段...")
            
            # 添加parent_id字段
            cursor.execute("""
                ALTER TABLE comments 
                ADD COLUMN parent_id INTEGER REFERENCES comments(id)
            """)
            
            # 提交更改
            conn.commit()
            print(f"  ✅ 成功添加parent_id字段")
            
            # 验证字段是否添加成功
            cursor.execute("PRAGMA table_info(comments)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'parent_id' in columns:
                print(f"  ✅ 验证成功：parent_id字段已添加")
            else:
                print(f"  ❌ 验证失败：parent_id字段未找到")
            
            # 关闭连接
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 迁移失败: {str(e)}")
            if 'conn' in locals():
                conn.close()

def verify_migration():
    """验证迁移结果"""
    print("\n验证迁移结果...")
    
    backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    db_paths = [
        os.path.join(backend_dir, 'app.db'),
        os.path.join(backend_dir, 'architectural_works.db')
    ]
    
    for db_path in db_paths:
        if not os.path.exists(db_path):
            continue
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(comments)")
            columns = cursor.fetchall()
            
            print(f"\n{db_path} - comments表结构:")
            for column in columns:
                print(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'}")
            
            # 检查是否有现有评论
            cursor.execute("SELECT COUNT(*) FROM comments")
            count = cursor.fetchone()[0]
            print(f"  现有评论数量: {count}")
            
            conn.close()
            
        except Exception as e:
            print(f"验证失败: {str(e)}")

if __name__ == "__main__":
    print("🔄 开始数据库迁移：添加评论回复功能")
    print("=" * 50)
    
    migrate_database()
    verify_migration()
    
    print("\n" + "=" * 50)
    print("✅ 数据库迁移完成！")
    print("\n新增功能:")
    print("  - comments表新增parent_id字段")
    print("  - 支持嵌套评论回复")
    print("  - 支持最多3层嵌套深度")
    print("\n后续步骤:")
    print("  1. 重启后端服务")
    print("  2. 测试评论回复功能")
    print("  3. 验证前端显示效果")
