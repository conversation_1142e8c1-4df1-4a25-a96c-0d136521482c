import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.database import SessionLocal, engine
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def remove_category_column():
    """从作品表中移除category字段"""
    db = SessionLocal()
    
    try:
        # 检查是否存在 category 字段 (SQLite)
        result = db.execute(text("PRAGMA table_info(architectural_works)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'category' not in columns:
            print("category 字段不存在，跳过迁移")
            return
        
        print("检测到 category 字段，开始移除...")
        
        # SQLite 不支持直接删除列，需要重建表
        # 1. 创建新表结构（不包含category）
        print("创建新表结构...")
        db.execute(text("""
            CREATE TABLE architectural_works_new (
                id INTEGER PRIMARY KEY,
                title VARCHAR NOT NULL,
                architect VARCHAR NOT NULL,
                year INTEGER NOT NULL,
                award VARCHAR NOT NULL,
                description TEXT NOT NULL,
                cover_image VARCHAR NOT NULL,
                images JSON,
                location VARCHAR,
                tags JSON,
                is_published BOOLEAN DEFAULT 0,
                is_featured BOOLEAN DEFAULT 0,
                likes_count INTEGER DEFAULT 0,
                views_count INTEGER DEFAULT 0,
                creator_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME,
                published_at DATETIME,
                FOREIGN KEY(creator_id) REFERENCES users (id)
            )
        """))
        
        # 2. 复制数据（排除category字段）
        print("复制数据到新表...")
        db.execute(text("""
            INSERT INTO architectural_works_new 
            (id, title, architect, year, award, description, cover_image, images, 
             location, tags, is_published, is_featured, likes_count, views_count, 
             creator_id, created_at, updated_at, published_at)
            SELECT id, title, architect, year, award, description, cover_image, images,
                   location, tags, is_published, is_featured, likes_count, views_count,
                   creator_id, created_at, updated_at, published_at
            FROM architectural_works
        """))
        
        # 3. 删除旧表
        print("删除旧表...")
        db.execute(text("DROP TABLE architectural_works"))
        
        # 4. 重命名新表
        print("重命名新表...")
        db.execute(text("ALTER TABLE architectural_works_new RENAME TO architectural_works"))
        
        # 5. 重建索引
        print("重建索引...")
        db.execute(text("CREATE INDEX ix_architectural_works_id ON architectural_works (id)"))
        db.execute(text("CREATE INDEX ix_architectural_works_title ON architectural_works (title)"))
        
        db.commit()
        print("category字段移除完成！")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    remove_category_column()
