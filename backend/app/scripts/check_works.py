import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def check_works():
    """Check current works and their image URLs"""
    db = SessionLocal()
    
    try:
        # Get all works
        works = db.query(ArchitecturalWork).all()
        
        print(f"Found {len(works)} works in database:\n")
        
        for work in works:
            print(f"ID: {work.id}")
            print(f"Title: {work.title}")
            print(f"Cover Image: {work.cover_image}")
            print(f"Images: {work.images}")
            print(f"Published: {work.is_published}")
            print("-" * 50)
            
    except Exception as e:
        print(f"Error checking works: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_works()
