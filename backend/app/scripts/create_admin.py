import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.core.database import SessionLocal, engine
from app.core.security import get_password_hash
from app.core.config import settings
# Import all models to ensure relationships are properly registered
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def create_admin_user():
    db = SessionLocal()
    
    try:
        # Check if admin already exists
        admin = db.query(User).filter(User.email == settings.ADMIN_EMAIL).first()
        if admin:
            print(f"✅ 管理员用户已存在: {admin.email}")
            return
        
        # Check if username already exists
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            print(f"✅ 用户名 'admin' 已存在，邮箱: {existing_user.email}")
            return
        
        # Create admin user
        print(f"正在创建管理员用户: {settings.ADMIN_EMAIL}")
        admin_user = User(
            email=settings.ADMIN_EMAIL,
            username="admin",
            hashed_password=get_password_hash(settings.ADMIN_PASSWORD),
            is_admin=True,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"✅ 管理员用户创建成功: {admin_user.email}")
        
    except IntegrityError as e:
        db.rollback()
        print(f"⚠️  管理员用户已存在或用户名冲突: {str(e)}")
        
        # Try to find existing admin
        admin = db.query(User).filter(
            (User.email == settings.ADMIN_EMAIL) | (User.username == "admin")
        ).first()
        if admin:
            print(f"✅ 找到现有管理员: {admin.email} (用户名: {admin.username})")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建管理员用户失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
