import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.work import ArchitecturalWork, Like, Favorite, Comment

def update_images_to_local():
    """Update existing works to use local images instead of external URLs"""
    db = SessionLocal()
    
    try:
        # Get all works
        works = db.query(ArchitecturalWork).all()
        
        if not works:
            print("No works found in database")
            return
        
        # Mapping of external URLs to local paths
        url_mapping = {
            "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00": "/uploads/works/samples/residential-1.jpg",
            "https://images.unsplash.com/photo-1558618666-fcd25c85cd64": "/uploads/works/samples/residential-2.jpg",
            "https://images.unsplash.com/photo-1554469384-e58fac16e23a": "/uploads/works/samples/residential-3.jpg",
            "https://images.unsplash.com/photo-1577495508048-b635879837f1": "/uploads/works/samples/cultural-1.jpg",
            "https://images.unsplash.com/photo-1504307651254-35680f356dfd": "/uploads/works/samples/cultural-2.jpg",
            "https://images.unsplash.com/photo-1541339907198-e08756dedf3f": "/uploads/works/samples/educational-1.jpg",
            "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab": "/uploads/works/samples/commercial-1.jpg",
            "https://images.unsplash.com/photo-1497366216548-37526070297c": "/uploads/works/samples/commercial-2.jpg"
        }
        
        updated_count = 0
        
        for work in works:
            updated = False
            
            # Update cover image
            if work.cover_image in url_mapping:
                old_cover = work.cover_image
                work.cover_image = url_mapping[work.cover_image]
                print(f"Updated cover image for '{work.title}': {old_cover} -> {work.cover_image}")
                updated = True
            
            # Update images array
            if work.images:
                new_images = []
                for img_url in work.images:
                    if img_url in url_mapping:
                        new_images.append(url_mapping[img_url])
                        print(f"Updated image for '{work.title}': {img_url} -> {url_mapping[img_url]}")
                        updated = True
                    else:
                        new_images.append(img_url)
                
                if updated:
                    work.images = new_images
            
            if updated:
                updated_count += 1
        
        # Commit changes
        if updated_count > 0:
            db.commit()
            print(f"\nSuccessfully updated {updated_count} works to use local images")
        else:
            print("No works needed updating")
            
    except Exception as e:
        print(f"Error updating images: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    update_images_to_local()
