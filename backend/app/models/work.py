from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base

class ArchitecturalWork(Base):
    __tablename__ = "architectural_works"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False, index=True)
    architect = Column(String, nullable=False)
    year = Column(Integer, nullable=False)
    award = Column(String, nullable=False)

    description = Column(Text, nullable=False)
    cover_image = Column(String, nullable=False)
    images = Column(JSON, default=[])
    location = Column(String, nullable=True)
    tags = Column(JSON, default=[])
    
    # Status and metrics
    is_published = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    likes_count = Column(Integer, default=0)
    views_count = Column(Integer, default=0)
    
    # Relationships
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships (simplified to avoid circular imports)
    # likes = relationship("Like", cascade="all, delete-orphan")
    # favorites = relationship("Favorite", cascade="all, delete-orphan")
    # comments = relationship("Comment", back_populates="work", cascade="all, delete-orphan")

class Like(Base):
    __tablename__ = "likes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    work_id = Column(Integer, ForeignKey("architectural_works.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships (simplified)
    # user = relationship("User")
    # work = relationship("ArchitecturalWork")

class Favorite(Base):
    __tablename__ = "favorites"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    work_id = Column(Integer, ForeignKey("architectural_works.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships (simplified)
    # user = relationship("User")
    # work = relationship("ArchitecturalWork")

class Comment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    work_id = Column(Integer, ForeignKey("architectural_works.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)  # 新增：父评论ID
    is_approved = Column(Boolean, default=False)  # 审核状态
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships (暂时注释掉避免循环导入问题)
    # user = relationship("User", foreign_keys=[user_id])
    # work = relationship("ArchitecturalWork", foreign_keys=[work_id])

    # 自引用关系：父评论和子回复
    # parent = relationship("Comment", remote_side=[id])
    # replies = relationship("Comment", cascade="all, delete-orphan")
