from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from ..core.database import get_db
from ..models.user import User
from ..models.work import ArchitecturalWork, Like, Favorite, Comment
from ..schemas.work import Work, WorkCreate, WorkUpdate, Comment as CommentSchema, CommentCreate
from ..api.deps import get_current_active_user, get_optional_current_user

router = APIRouter()

@router.get("/", response_model=List[Work])
def get_works(
    skip: int = 0,
    limit: int = 100,
    year: Optional[int] = None,
    award: Optional[str] = None,
    search: Optional[str] = None,
    featured: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    query = db.query(ArchitecturalWork).filter(ArchitecturalWork.is_published == True)
    
    # Apply filters
    
    if year:
        query = query.filter(ArchitecturalWork.year == year)
    
    if award and award != "全部":
        query = query.filter(ArchitecturalWork.award == award)
    
    if search:
        query = query.filter(
            or_(
                ArchitecturalWork.title.ilike(f"%{search}%"),
                ArchitecturalWork.architect.ilike(f"%{search}%"),
                ArchitecturalWork.description.ilike(f"%{search}%")
            )
        )
    
    if featured is not None:
        query = query.filter(ArchitecturalWork.is_featured == featured)

    # 按照奖项等级和ID排序（与Excel表格顺序一致）
    # 奖项优先级：一等奖 > 二等奖 > 三等奖 > 优秀奖 > 入围奖
    award_order = {
        '一等奖': 1,
        '二等奖': 2,
        '三等奖': 3,
        '优秀奖': 4,
        '入围奖': 5
    }

    # 使用CASE WHEN进行排序
    from sqlalchemy import case
    award_case = case(
        (ArchitecturalWork.award == '一等奖', 1),
        (ArchitecturalWork.award == '二等奖', 2),
        (ArchitecturalWork.award == '三等奖', 3),
        (ArchitecturalWork.award == '优秀奖', 4),
        (ArchitecturalWork.award == '入围奖', 5),
        else_=6
    )

    works = query.order_by(award_case, ArchitecturalWork.id).offset(skip).limit(limit).all()
    
    # Add user interaction data
    result = []
    for work in works:
        work_dict = work.__dict__.copy()
        work_dict['is_liked_by_user'] = False
        work_dict['is_favorite_by_user'] = False
        
        if current_user:
            like = db.query(Like).filter(
                and_(Like.user_id == current_user.id, Like.work_id == work.id)
            ).first()
            work_dict['is_liked_by_user'] = like is not None
            
            favorite = db.query(Favorite).filter(
                and_(Favorite.user_id == current_user.id, Favorite.work_id == work.id)
            ).first()
            work_dict['is_favorite_by_user'] = favorite is not None
        
        result.append(Work(**work_dict))
    
    return result

@router.get("/{work_id}", response_model=Work)
def get_work(
    work_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    try:
        work = db.query(ArchitecturalWork).filter(
            and_(ArchitecturalWork.id == work_id, ArchitecturalWork.is_published == True)
        ).first()

        if not work:
            raise HTTPException(status_code=404, detail="Work not found")

        # Increment view count
        work.views_count += 1
        db.commit()

        # Add user interaction data
        is_liked_by_user = False
        is_favorite_by_user = False

        if current_user:
            like = db.query(Like).filter(
                and_(Like.user_id == current_user.id, Like.work_id == work.id)
            ).first()
            is_liked_by_user = like is not None

            favorite = db.query(Favorite).filter(
                and_(Favorite.user_id == current_user.id, Favorite.work_id == work.id)
            ).first()
            is_favorite_by_user = favorite is not None

        # Create response object with proper field mapping
        work_dict = {
            'id': work.id,
            'title': work.title,
            'architect': work.architect,
            'year': work.year,
            'award': work.award,
            'description': work.description,
            'cover_image': work.cover_image,
            'images': work.images or [],
            'location': work.location,
            'tags': work.tags or [],
            'is_published': work.is_published,
            'is_featured': work.is_featured,
            'likes_count': work.likes_count,
            'views_count': work.views_count,
            'creator_id': work.creator_id,
            'created_at': work.created_at.isoformat() if work.created_at else None,
            'updated_at': work.updated_at.isoformat() if work.updated_at else None,
            'published_at': work.published_at.isoformat() if work.published_at else None,
            'is_liked_by_user': is_liked_by_user,
            'is_favorite_by_user': is_favorite_by_user
        }

        return Work(**work_dict)
    except Exception as e:
        print(f"Error in get_work: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/{work_id}/like")
def toggle_like(
    work_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    existing_like = db.query(Like).filter(
        and_(Like.user_id == current_user.id, Like.work_id == work_id)
    ).first()
    
    if existing_like:
        # Unlike
        db.delete(existing_like)
        work.likes_count -= 1
        liked = False
    else:
        # Like
        new_like = Like(user_id=current_user.id, work_id=work_id)
        db.add(new_like)
        work.likes_count += 1
        liked = True
    
    db.commit()
    return {"liked": liked, "likes_count": work.likes_count}

@router.post("/{work_id}/favorite")
def toggle_favorite(
    work_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    existing_favorite = db.query(Favorite).filter(
        and_(Favorite.user_id == current_user.id, Favorite.work_id == work_id)
    ).first()
    
    if existing_favorite:
        # Remove favorite
        db.delete(existing_favorite)
        favorited = False
    else:
        # Add favorite
        new_favorite = Favorite(user_id=current_user.id, work_id=work_id)
        db.add(new_favorite)
        favorited = True
    
    db.commit()
    return {"favorited": favorited}

def organize_comments(comments, db):
    """将扁平的评论列表组织为嵌套结构"""
    from app.models.user import User

    comment_map = {}
    root_comments = []

    # 首先创建所有评论的映射
    for comment in comments:
        comment_dict = comment.__dict__.copy()

        # 手动查询用户信息（因为关系被简化了）
        user = db.query(User).filter(User.id == comment.user_id).first()
        comment_dict['username'] = user.username if user else "Unknown"
        comment_dict['user_avatar'] = user.avatar_url if user else None
        comment_dict['replies'] = []

        # 如果有父评论，获取父评论的用户名
        if comment.parent_id:
            parent_comment = db.query(Comment).filter(Comment.id == comment.parent_id).first()
            if parent_comment:
                parent_user = db.query(User).filter(User.id == parent_comment.user_id).first()
                comment_dict['reply_to_username'] = parent_user.username if parent_user else "Unknown"
            else:
                comment_dict['reply_to_username'] = None
        else:
            comment_dict['reply_to_username'] = None

        comment_map[comment.id] = comment_dict

    # 然后组织父子关系
    for comment in comments:
        comment_dict = comment_map[comment.id]
        if comment.parent_id and comment.parent_id in comment_map:
            parent = comment_map[comment.parent_id]
            parent['replies'].append(comment_dict)
        else:
            root_comments.append(comment_dict)

    return root_comments

@router.get("/{work_id}/comments", response_model=List[CommentSchema])
def get_comments(work_id: int, db: Session = Depends(get_db)):
    # 只显示已审核的评论，包括回复
    comments = db.query(Comment).filter(
        and_(Comment.work_id == work_id, Comment.is_approved == True)
    ).order_by(Comment.created_at.asc()).all()

    # 组织为嵌套结构
    organized_comments = organize_comments(comments, db)

    result = []
    for comment_dict in organized_comments:
        result.append(CommentSchema(**comment_dict))

    return result

@router.post("/{work_id}/comments", response_model=CommentSchema)
def create_comment(
    work_id: int,
    comment: CommentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not work:
        raise HTTPException(status_code=404, detail="Work not found")

    # 如果是回复，验证父评论是否存在
    parent_comment = None
    if comment.parent_id:
        parent_comment = db.query(Comment).filter(
            and_(Comment.id == comment.parent_id, Comment.work_id == work_id)
        ).first()
        if not parent_comment:
            raise HTTPException(status_code=404, detail="Parent comment not found")

    db_comment = Comment(
        content=comment.content,
        user_id=current_user.id,
        work_id=work_id,
        parent_id=comment.parent_id,
        is_approved=False  # 新评论默认未审核
    )

    db.add(db_comment)
    db.commit()
    db.refresh(db_comment)

    # Return comment with user info
    comment_dict = db_comment.__dict__.copy()
    comment_dict['username'] = current_user.username
    comment_dict['user_avatar'] = current_user.avatar_url
    comment_dict['replies'] = []

    # 如果是回复，添加回复对象的用户名
    if parent_comment:
        comment_dict['reply_to_username'] = parent_comment.user.username
    else:
        comment_dict['reply_to_username'] = None

    return CommentSchema(**comment_dict)

@router.post("/{work_id}/comments/{comment_id}/reply", response_model=CommentSchema)
def reply_to_comment(
    work_id: int,
    comment_id: int,
    comment: CommentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    # 验证作品存在
    work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not work:
        raise HTTPException(status_code=404, detail="Work not found")

    # 验证父评论存在
    parent_comment = db.query(Comment).filter(
        and_(Comment.id == comment_id, Comment.work_id == work_id)
    ).first()
    if not parent_comment:
        raise HTTPException(status_code=404, detail="Parent comment not found")

    # 创建回复
    db_comment = Comment(
        content=comment.content,
        user_id=current_user.id,
        work_id=work_id,
        parent_id=comment_id,
        is_approved=False  # 新回复默认未审核
    )

    db.add(db_comment)
    db.commit()
    db.refresh(db_comment)

    # Return comment with user info
    comment_dict = db_comment.__dict__.copy()
    comment_dict['username'] = current_user.username
    comment_dict['user_avatar'] = current_user.avatar_url
    comment_dict['reply_to_username'] = parent_comment.user.username
    comment_dict['replies'] = []

    return CommentSchema(**comment_dict)
