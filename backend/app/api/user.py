from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List

from ..core.database import get_db
from ..models.user import User
from ..models.work import ArchitecturalWork, Like, Favorite
from ..schemas.work import Work
from ..schemas.user import ChangePasswordRequest, UpdateProfileRequest, User as UserSchema
from .deps import get_current_active_user
from ..core.security import verify_password, get_password_hash

router = APIRouter()

@router.get("/liked-works", response_model=List[Work])
def get_user_liked_works(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户点赞的作品"""
    try:
        # 获取用户点赞的作品ID
        liked_work_ids = db.query(Like.work_id).filter(Like.user_id == current_user.id).all()
        liked_work_ids = [work_id[0] for work_id in liked_work_ids]
        
        if not liked_work_ids:
            return []
        
        # 获取作品详情
        works = db.query(ArchitecturalWork).filter(
            and_(
                ArchitecturalWork.id.in_(liked_work_ids),
                ArchitecturalWork.is_published == True
            )
        ).all()
        
        # 转换为响应格式
        result = []
        for work in works:
            work_dict = {
                'id': work.id,
                'title': work.title,
                'architect': work.architect,
                'year': work.year,
                'award': work.award,
                'description': work.description,
                'cover_image': work.cover_image,
                'images': work.images or [],
                'location': work.location,
                'tags': work.tags or [],
                'is_published': work.is_published,
                'is_featured': work.is_featured,
                'likes_count': work.likes_count,
                'views_count': work.views_count,
                'creator_id': work.creator_id,
                'created_at': work.created_at.isoformat() if work.created_at else None,
                'updated_at': work.updated_at.isoformat() if work.updated_at else None,
                'published_at': work.published_at.isoformat() if work.published_at else None,
                'is_liked_by_user': True,  # 用户点赞的作品
                'is_favorite_by_user': False  # 需要检查是否也收藏了
            }
            
            # 检查是否也收藏了
            favorite = db.query(Favorite).filter(
                and_(Favorite.user_id == current_user.id, Favorite.work_id == work.id)
            ).first()
            work_dict['is_favorite_by_user'] = favorite is not None
            
            result.append(Work(**work_dict))
        
        return result
    except Exception as e:
        print(f"Error in get_user_liked_works: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/favorite-works", response_model=List[Work])
def get_user_favorite_works(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户收藏的作品"""
    try:
        # 获取用户收藏的作品ID
        favorite_work_ids = db.query(Favorite.work_id).filter(Favorite.user_id == current_user.id).all()
        favorite_work_ids = [work_id[0] for work_id in favorite_work_ids]
        
        if not favorite_work_ids:
            return []
        
        # 获取作品详情
        works = db.query(ArchitecturalWork).filter(
            and_(
                ArchitecturalWork.id.in_(favorite_work_ids),
                ArchitecturalWork.is_published == True
            )
        ).all()
        
        # 转换为响应格式
        result = []
        for work in works:
            work_dict = {
                'id': work.id,
                'title': work.title,
                'architect': work.architect,
                'year': work.year,
                'award': work.award,
                'description': work.description,
                'cover_image': work.cover_image,
                'images': work.images or [],
                'location': work.location,
                'tags': work.tags or [],
                'is_published': work.is_published,
                'is_featured': work.is_featured,
                'likes_count': work.likes_count,
                'views_count': work.views_count,
                'creator_id': work.creator_id,
                'created_at': work.created_at.isoformat() if work.created_at else None,
                'updated_at': work.updated_at.isoformat() if work.updated_at else None,
                'published_at': work.published_at.isoformat() if work.published_at else None,
                'is_liked_by_user': False,  # 需要检查是否也点赞了
                'is_favorite_by_user': True  # 用户收藏的作品
            }
            
            # 检查是否也点赞了
            like = db.query(Like).filter(
                and_(Like.user_id == current_user.id, Like.work_id == work.id)
            ).first()
            work_dict['is_liked_by_user'] = like is not None
            
            result.append(Work(**work_dict))
        
        return result
    except Exception as e:
        print(f"Error in get_user_favorite_works: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/change-password")
def change_password(
    password_data: ChangePasswordRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """修改用户密码"""
    try:
        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="当前密码不正确")
        
        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        db.commit()
        
        return {"message": "密码修改成功"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in change_password: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/profile", response_model=UserSchema)
def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """获取用户个人资料"""
    return current_user

@router.put("/profile", response_model=UserSchema)
def update_user_profile(
    profile_data: UpdateProfileRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新用户个人资料"""
    try:
        # 检查用户名是否已被其他用户使用
        if profile_data.username and profile_data.username != current_user.username:
            existing_user = db.query(User).filter(
                and_(User.username == profile_data.username, User.id != current_user.id)
            ).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="用户名已被使用")

        # 更新用户信息
        if profile_data.username is not None:
            current_user.username = profile_data.username
        if profile_data.avatar_url is not None:
            current_user.avatar_url = profile_data.avatar_url

        db.commit()
        db.refresh(current_user)

        return current_user
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in update_user_profile: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
