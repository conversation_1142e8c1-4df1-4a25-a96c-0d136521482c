from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from ..core.database import get_db
from ..models.user import User
from ..models.work import ArchitecturalWork, Comment
from ..schemas.work import WorkAdmin, WorkCreate, WorkUpdate, CommentAdmin
from ..schemas.user import User as UserSchema, AdminUserUpdate, AdminUserCreate
from ..api.deps import get_current_admin_user

router = APIRouter()

@router.get("/works", response_model=List[WorkAdmin])
def get_all_works(
    skip: int = 0,
    limit: int = 100,
    published: bool = None,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    query = db.query(ArchitecturalWork)
    
    if published is not None:
        query = query.filter(ArchitecturalWork.is_published == published)

    # 按照奖项等级和ID排序（与Excel表格顺序一致）
    from sqlalchemy import case
    award_case = case(
        (ArchitecturalWork.award == '一等奖', 1),
        (ArchitecturalWork.award == '二等奖', 2),
        (ArchitecturalWork.award == '三等奖', 3),
        (ArchitecturalWork.award == '优秀奖', 4),
        (ArchitecturalWork.award == '入围奖', 5),
        else_=6
    )

    return query.order_by(award_case, ArchitecturalWork.id).offset(skip).limit(limit).all()

@router.get("/works/{work_id}", response_model=WorkAdmin)
def get_work(
    work_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not work:
        raise HTTPException(status_code=404, detail="Work not found")
    return work

@router.post("/works", response_model=WorkAdmin)
def create_work(
    work: WorkCreate,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = ArchitecturalWork(**work.dict(), creator_id=current_admin.id)
    db.add(db_work)
    db.commit()
    db.refresh(db_work)
    return db_work

@router.put("/works/{work_id}", response_model=WorkAdmin)
def update_work(
    work_id: int,
    work_update: WorkUpdate,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not db_work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    update_data = work_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_work, field, value)
    
    db.commit()
    db.refresh(db_work)
    return db_work

@router.post("/works/{work_id}/publish")
def publish_work(
    work_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not db_work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    db_work.is_published = True
    db_work.published_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Work published successfully"}

@router.post("/works/{work_id}/unpublish")
def unpublish_work(
    work_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not db_work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    db_work.is_published = False
    db.commit()
    
    return {"message": "Work unpublished successfully"}

@router.post("/works/{work_id}/feature")
def toggle_featured(
    work_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not db_work:
        raise HTTPException(status_code=404, detail="Work not found")
    
    db_work.is_featured = not db_work.is_featured
    db.commit()
    
    return {"message": f"Work {'featured' if db_work.is_featured else 'unfeatured'} successfully"}

@router.delete("/works/{work_id}")
def delete_work(
    work_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    db_work = db.query(ArchitecturalWork).filter(ArchitecturalWork.id == work_id).first()
    if not db_work:
        raise HTTPException(status_code=404, detail="Work not found")

    db.delete(db_work)
    db.commit()

    return {"message": "Work deleted successfully"}

# 评论管理
@router.get("/comments", response_model=List[CommentAdmin])
def get_all_comments(
    skip: int = 0,
    limit: int = 100,
    approved: str = None,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """获取所有评论（管理员）"""
    try:
        from app.models.user import User
        from app.models.work import ArchitecturalWork

        # 使用JOIN查询而不是关系
        query = db.query(
            Comment,
            User.username,
            User.avatar,
            ArchitecturalWork.title
        ).join(
            User, Comment.user_id == User.id
        ).join(
            ArchitecturalWork, Comment.work_id == ArchitecturalWork.id
        )

        if approved is not None:
            # 转换字符串参数为布尔值
            approved_bool = approved.lower() == 'true'
            query = query.filter(Comment.is_approved == approved_bool)

        results = query.order_by(Comment.created_at.desc()).offset(skip).limit(limit).all()

        result = []
        for comment, username, user_avatar, work_title in results:
            comment_dict = {
                "id": comment.id,
                "work_id": comment.work_id,
                "user_id": comment.user_id,
                "username": username,
                "user_avatar": user_avatar,
                "work_title": work_title,
                "content": comment.content,
                "created_at": comment.created_at,
                "is_approved": comment.is_approved,
                "parent_id": comment.parent_id
            }
            result.append(CommentAdmin(**comment_dict))

        return result
    except Exception as e:
        print(f"Error in get_all_comments: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/comments/{comment_id}/approve")
def approve_comment(
    comment_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """审核通过评论"""
    comment = db.query(Comment).filter(Comment.id == comment_id).first()
    if not comment:
        raise HTTPException(status_code=404, detail="Comment not found")

    comment.is_approved = True
    db.commit()

    return {"message": "Comment approved successfully"}

@router.post("/comments/{comment_id}/reject")
def reject_comment(
    comment_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """拒绝评论"""
    comment = db.query(Comment).filter(Comment.id == comment_id).first()
    if not comment:
        raise HTTPException(status_code=404, detail="Comment not found")

    comment.is_approved = False
    db.commit()

    return {"message": "Comment rejected successfully"}

@router.delete("/comments/{comment_id}")
def delete_comment(
    comment_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """删除评论"""
    comment = db.query(Comment).filter(Comment.id == comment_id).first()
    if not comment:
        raise HTTPException(status_code=404, detail="Comment not found")

    db.delete(comment)
    db.commit()

    return {"message": "Comment deleted successfully"}

@router.get("/users", response_model=List[UserSchema])
def get_all_users(
    skip: int = 0,
    limit: int = 100,
    search: str = None,
    is_admin: bool = None,
    is_active: bool = None,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """获取所有用户（管理员）"""
    query = db.query(User)

    # 搜索过滤
    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.email.contains(search))
        )

    # 管理员状态过滤
    if is_admin is not None:
        query = query.filter(User.is_admin == is_admin)

    # 激活状态过滤
    if is_active is not None:
        query = query.filter(User.is_active == is_active)

    return query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()

@router.get("/users/{user_id}", response_model=UserSchema)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """获取单个用户信息（管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.post("/users", response_model=UserSchema)
def create_user(
    user_data: AdminUserCreate,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """创建新用户（管理员）"""
    from ..core.security import get_password_hash

    # 检查邮箱是否已存在
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(status_code=400, detail="Email already registered")

    # 检查用户名是否已存在
    if db.query(User).filter(User.username == user_data.username).first():
        raise HTTPException(status_code=400, detail="Username already taken")

    # 创建用户
    db_user = User(
        email=user_data.email,
        username=user_data.username,
        hashed_password=get_password_hash(user_data.password),
        is_admin=user_data.is_admin,
        is_active=True
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return db_user

@router.put("/users/{user_id}", response_model=UserSchema)
def update_user(
    user_id: int,
    user_data: AdminUserUpdate,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """更新用户信息（管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # 检查邮箱是否已被其他用户使用
    if user_data.email and user_data.email != user.email:
        if db.query(User).filter(User.email == user_data.email, User.id != user_id).first():
            raise HTTPException(status_code=400, detail="Email already in use")

    # 检查用户名是否已被其他用户使用
    if user_data.username and user_data.username != user.username:
        if db.query(User).filter(User.username == user_data.username, User.id != user_id).first():
            raise HTTPException(status_code=400, detail="Username already taken")

    # 防止管理员取消自己的管理员权限
    if user.id == current_admin.id and user_data.is_admin is False:
        raise HTTPException(status_code=400, detail="Cannot remove admin privileges from your own account")

    # 防止管理员禁用自己
    if user.id == current_admin.id and user_data.is_active is False:
        raise HTTPException(status_code=400, detail="Cannot disable your own account")

    # 更新用户信息
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)

    db.commit()
    db.refresh(user)

    return user

@router.post("/users/{user_id}/toggle-admin")
def toggle_user_admin(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.is_admin = not user.is_admin
    db.commit()

    return {"message": f"User {'promoted to' if user.is_admin else 'demoted from'} admin"}

@router.post("/users/{user_id}/toggle-active")
def toggle_user_active(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """切换用户激活状态（禁用/启用）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # 防止管理员禁用自己
    if user.id == current_admin.id:
        raise HTTPException(status_code=400, detail="Cannot disable your own account")

    user.is_active = not user.is_active
    db.commit()

    return {"message": f"User {'activated' if user.is_active else 'deactivated'} successfully"}

@router.delete("/users/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """删除用户"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # 防止管理员删除自己
    if user.id == current_admin.id:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")

    # 删除用户相关的数据
    # 删除用户的评论
    db.query(Comment).filter(Comment.user_id == user_id).delete()

    # 删除用户的点赞记录
    from ..models.work import Like
    db.query(Like).filter(Like.user_id == user_id).delete()

    # 删除用户的收藏记录
    from ..models.work import Favorite
    db.query(Favorite).filter(Favorite.user_id == user_id).delete()

    # 删除用户
    db.delete(user)
    db.commit()

    return {"message": "User deleted successfully"}

@router.get("/stats")
def get_admin_stats(
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """获取管理后台统计数据"""
    try:
        # 统计作品数量
        total_works = db.query(ArchitecturalWork).count()
        published_works = db.query(ArchitecturalWork).filter(ArchitecturalWork.is_published == True).count()

        # 统计用户数量
        total_users = db.query(User).count()
        admin_users = db.query(User).filter(User.is_admin == True).count()

        # 统计总浏览量和点赞数
        from sqlalchemy import func
        total_views = db.query(func.sum(ArchitecturalWork.views_count)).scalar() or 0
        total_likes = db.query(func.sum(ArchitecturalWork.likes_count)).scalar() or 0

        # 统计评论数量
        total_comments = db.query(Comment).count()
        pending_comments = db.query(Comment).filter(Comment.is_approved == False).count()

        return {
            "total_works": total_works,
            "published_works": published_works,
            "draft_works": total_works - published_works,
            "total_users": total_users,
            "admin_users": admin_users,
            "regular_users": total_users - admin_users,
            "total_views": total_views,
            "total_likes": total_likes,
            "total_comments": total_comments,
            "pending_comments": pending_comments,
            "approved_comments": total_comments - pending_comments
        }
    except Exception as e:
        print(f"Error in get_admin_stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
