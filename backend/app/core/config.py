from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # Database - 支持SQLite用于容器部署，使用绝对路径
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:////opt/hypcup-event/data/app.db")

    # JWT
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS - 添加容器内部通信和局域网访问
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://*************:3000",
        "http://*************:8000",
        "***********:3000"
        "***********:8000"
        # 允许所有局域网地址（开发/测试环境）
        "*"
    ]

    # File Upload
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "uploads")
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB

    # Admin
    ADMIN_EMAIL: str = os.getenv("ADMIN_EMAIL", "<EMAIL>")
    ADMIN_PASSWORD: str = os.getenv("ADMIN_PASSWORD", "admin123")

    class Config:
        env_file = ".env"

settings = Settings()