from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
from .core.config import settings
from .core.database import engine
from .models import user, work
from .api import auth, works, admin, upload
from .api import user as user_api

# Create tables
user.Base.metadata.create_all(bind=engine)
work.Base.metadata.create_all(bind=engine)

app = FastAPI(title="Architectural Works API", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create upload directory
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

# Static files
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_DIR), name="uploads")

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(works.router, prefix="/api/works", tags=["works"])
app.include_router(admin.router, prefix="/api/admin", tags=["admin"])
app.include_router(upload.router, prefix="/api/upload", tags=["upload"])
app.include_router(user_api.router, prefix="/api/user", tags=["user"])

@app.get("/")
def read_root():
    return {"message": "Architectural Works API"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}
