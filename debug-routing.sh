#!/bin/bash

# Hypcup Event Platform 路由调试脚本
# 用于诊断反向代理和路由问题

echo "🔍 Hypcup Event Platform 路由调试"
echo "=================================="

# 检查容器状态
echo "📊 检查容器状态..."
docker compose ps

echo ""
echo "🌐 检查服务连通性..."

# 检查前端服务
echo "检查前端服务 (localhost:3000)..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:3000/ || echo "❌ 前端服务无法访问"

# 检查后端服务
echo "检查后端服务 (localhost:8000)..."
curl -s -o /dev/null -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n" http://localhost:8000/ || echo "❌ 后端服务无法访问"

# 检查后端健康状态
echo "检查后端健康状态..."
curl -s http://localhost:8000/health || echo "❌ 后端健康检查失败"

echo ""
echo "📝 检查日志..."

# 显示最近的前端日志
echo "前端日志 (最近10行):"
tail -n 10 data/log/frontend.out.log 2>/dev/null || echo "❌ 无法读取前端日志"

echo ""
echo "后端日志 (最近10行):"
tail -n 10 data/log/backend.out.log 2>/dev/null || echo "❌ 无法读取后端日志"

echo ""
echo "🔧 环境变量检查..."

# 检查容器内的环境变量
echo "检查容器内环境变量..."
docker compose exec hypcup-event env | grep -E "(NODE_ENV|NEXT_PUBLIC_API_URL|PORT)" || echo "❌ 无法获取环境变量"

echo ""
echo "🌍 测试具体路径..."

# 测试根路径
echo "测试根路径 (/):"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3000/

# 测试API路径
echo "测试API路径 (/api/works):"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3000/api/works

# 测试作品详情页
echo "测试作品详情页 (/work/63):"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3000/work/63

echo ""
echo "📋 Next.js 路由信息..."

# 检查 Next.js 构建信息
echo "检查 Next.js 构建状态..."
docker compose exec hypcup-event ls -la /opt/hypcup-event/frontend/.next/ 2>/dev/null || echo "❌ 无法访问 .next 目录"

echo ""
echo "🔍 网络连接测试..."

# 测试容器内部网络
echo "测试容器内部连接..."
docker compose exec hypcup-event curl -s -o /dev/null -w "内部前端连接: %{http_code}\n" http://localhost:3000/ 2>/dev/null || echo "❌ 容器内部前端连接失败"
docker compose exec hypcup-event curl -s -o /dev/null -w "内部后端连接: %{http_code}\n" http://localhost:8000/ 2>/dev/null || echo "❌ 容器内部后端连接失败"

echo ""
echo "📊 进程状态..."

# 检查容器内进程
echo "检查容器内进程..."
docker compose exec hypcup-event ps aux | grep -E "(node|python|uvicorn)" || echo "❌ 无法获取进程信息"

echo ""
echo "🎯 建议的解决方案..."

echo "1. 如果前端服务无法访问，检查 Next.js 构建是否成功"
echo "2. 如果后端服务无法访问，检查 Python 依赖和数据库连接"
echo "3. 如果根路径返回404，检查 Nginx 反向代理配置"
echo "4. 如果API调用失败，检查 CORS 和环境变量配置"
echo "5. 检查防火墙和端口映射设置"

echo ""
echo "🔧 快速修复命令..."
echo "重启服务: docker compose restart"
echo "重新构建: docker compose up --build -d"
echo "查看实时日志: docker compose logs -f"
echo "进入容器调试: docker compose exec hypcup-event bash"
