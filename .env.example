# Hypcup Event Platform 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=production
PORT=3000

# ===========================================
# 数据库配置
# ===========================================
# SQLite数据库文件路径（容器内绝对路径）
DATABASE_URL=sqlite:////opt/hypcup-event/data/app.db

# 文件上传目录
UPLOAD_DIR=/opt/hypcup-event/uploads

# ===========================================
# API配置
# ===========================================
# 前端访问后端API的地址
# 容器内部通信使用 localhost
# 如需外部访问，改为服务器IP地址
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# ===========================================
# 安全配置
# ===========================================
# JWT密钥（生产环境请使用强密码）
SECRET_KEY=ZaNTW3rPyFWnMhptPQEv8CmGtbJhMPVe8x2Q

# 管理员账户
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=123456

# ===========================================
# 前端构建配置
# ===========================================
PACKAGE_MANAGER=pnpm
CONTAINER_PACKAGE_URL=https://registry.npmmirror.com/

# ===========================================
# 可选配置
# ===========================================
# JWT算法
ALGORITHM=HS256

# Token过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传大小限制（字节）
MAX_FILE_SIZE=10485760

# ===========================================
# 开发环境配置（仅开发时使用）
# ===========================================
# 如果在开发环境，可以使用以下配置
# NODE_ENV=development
# NEXT_PUBLIC_API_URL=http://localhost:8000/api
# DATABASE_URL=sqlite:///./app.db
