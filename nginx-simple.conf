# 修复后的 Nginx 配置
# 适用于 comp.uedmagazine.net

server {
    listen 80;
    listen 443 ssl http2;
    server_name comp.uedmagazine.net;

    # SSL 配置（请根据实际情况配置）
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # 日志配置
    access_log /var/log/nginx/hypcup_access.log combined;
    error_log /var/log/nginx/hypcup_error.log warn;

    # 客户端上传大小限制
    client_max_body_size 10M;

    # 后端 API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 调试头
        add_header X-Debug-Backend "true" always;
    }

    # 后端健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
        access_log off;
    }

    # 上传文件代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:8000/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Next.js 静态资源
    location /_next/static/ {
        proxy_pass http://127.0.0.1:3000/_next/static/;
        proxy_set_header Host $host;

        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Next.js 图片优化
    location /_next/image {
        proxy_pass http://127.0.0.1:3000/_next/image;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 前端应用代理 - 所有其他请求（必须放在最后）
    location / {
        proxy_pass http://127.0.0.1:3000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 调试头
        add_header X-Debug-Frontend "true" always;

        # 错误处理
        proxy_intercept_errors off;
    }
}
