 ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3000". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
Error fetching work on server: Error: HTTP 500
    at o (.next/server/app/work/[id]/page.js:1:3013)
    at async d (.next/server/app/work/[id]/page.js:1:3544)
Error fetching work on server: Error: HTTP 500
    at o (.next/server/app/work/[id]/page.js:1:3013)
    at async Module.l (.next/server/app/work/[id]/page.js:1:3182)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
Frontend API - Error: Error: HTTP 500: {"detail":"type object 'User' has no attribute 'avatar'"}
    at u (.next/server/app/api/admin/comments/route.js:1:1212)
