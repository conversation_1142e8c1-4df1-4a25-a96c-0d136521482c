Starting frontend server...

> hypcup-event@0.1.0 start /opt/hypcup-event/frontend
> next start

   ▲ Next.js 15.2.4
   - Local:        http://localhost:3000
   - Network:      http://**********:3000

 ✓ Starting...
 ✓ Ready in 710ms
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: '112233' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
