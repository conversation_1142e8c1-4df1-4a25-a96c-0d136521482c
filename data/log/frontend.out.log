Starting frontend server...

> hypcup-event@0.1.0 start /opt/hypcup-event/frontend
> next start

   ▲ Next.js 15.2.4
   - Local:        http://localhost:3000
   - Network:      http://**********:3000

 ✓ Starting...
 ✓ Ready in 659ms
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:13:17 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":2}
Backend response status: 200
Backend response headers: {
  'content-length': '31',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:13:23 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":false,"likes_count":1}
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:13:24 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":2}
Backend response status: 200
Backend response headers: {
  'content-length': '19',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:13:25 GMT',
  server: 'uvicorn'
}
Backend success response: {"favorited":false}
Frontend API - Auth header: Missing
Frontend API - No auth header found
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: 'gogogo000' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
Frontend API - Admin work GET request for ID: 146
Frontend API - Backend response status: 200
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
Frontend API - Admin work GET request for ID: 46
Frontend API - Backend response status: 200
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: '112233' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
代理请求: GET http://localhost:8000/api/works/146
代理请求: GET http://localhost:8000/api/works/146
Backend response status: 200
Backend response headers: {
  'content-length': '18',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:16:15 GMT',
  server: 'uvicorn'
}
Backend success response: {"favorited":true}
代理请求: GET http://localhost:8000/api/works/146
代理请求: GET http://localhost:8000/api/works/146
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: 'gogogo000' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 401
Backend error: {"detail":"Incorrect email or password"}
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: 'gogogo000' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
代理请求: GET http://localhost:8000/api/works/146
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
Frontend API - Admin works GET request
Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/admin/works
Frontend API - Backend response status: 200
Frontend API - Success, returning data
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
=== Frontend Admin Users API ===
Backend URL: http://localhost:8000/api/admin/users
Authorization: Bearer eyJhbGciOiJIU...
Backend response status: 200
Admin users retrieved successfully
代理请求: GET http://localhost:8000/api/works/146
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: '112233' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
代理请求: GET http://localhost:8000/api/works/146
代理请求: GET http://localhost:8000/api/works/46
代理请求: GET http://localhost:8000/api/works/59
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:18:56 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":1}
代理请求: GET http://localhost:8000/api/works/34
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:19:01 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":1}
代理请求: GET http://localhost:8000/api/works/75
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:19:09 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":1}
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
Frontend API - Auth header: Missing
Frontend API - No auth header found
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: '112233' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
代理请求: GET http://localhost:8000/api/works/63
Backend response status: 200
Backend response headers: {
  'content-length': '30',
  'content-type': 'application/json',
  date: 'Tue, 05 Aug 2025 02:31:39 GMT',
  server: 'uvicorn'
}
Backend success response: {"liked":true,"likes_count":1}
代理请求: GET http://localhost:8000/api/works/63
代理请求: GET http://localhost:8000/api/works/146
=== Frontend Login API ===
API_BASE_URL: http://localhost:8000/api
Backend URL: http://localhost:8000/api/auth/login
Form data: { username: '<EMAIL>', password: 'gogogo000' }
Login attempt: { username: '<EMAIL>', password: '***' }
Backend response status: 200
Login successful, returning data
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
代理请求: GET http://localhost:8000/api/works/146
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
Frontend API - Auth header: Present
Frontend API - Calling backend URL: http://localhost:8000/api/auth/me
Frontend API - Backend response status: 200
Frontend API - Success, returning user data
Frontend API - Auth header: Present
Frontend API - Approved param: null
Frontend API - Calling backend URL: http://localhost:8000/api/admin/comments
Frontend API - Backend response status: 500
Frontend API - Backend error: {"detail":"type object 'User' has no attribute 'avatar'"}
