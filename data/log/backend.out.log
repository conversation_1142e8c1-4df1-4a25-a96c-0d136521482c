Starting backend server...
Database tables created successfully
INFO:     127.0.0.1:56344 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:40004 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57626 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51528 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56040 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42244 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48936 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43940 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51194 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39714 - "GET /health HTTP/1.1" 200 OK
