Starting backend server...
Database tables created successfully
INFO:     127.0.0.1:38748 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57382 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50280 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59912 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59624 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59624 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:59814 - "GET /health HTTP/1.1" 200 OK
Error in get_work: 
INFO:     127.0.0.1:59824 - "GET /api/works/164 HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:59824 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:59824 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59824 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59824 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:59824 - "POST /api/works/146/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "POST /api/works/146/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "POST /api/works/146/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "POST /api/works/146/favorite HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "GET /api/user/liked-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:53858 - "GET /api/user/favorite-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:36488 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:47210 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39284 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39300 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39300 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:39300 - "GET /api/works/ HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:39300 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:39300 - "GET /api/admin/works HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:39300 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:39300 - "GET /api/admin/users HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "POST /api/admin/users/3/toggle-admin HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "GET /api/admin/users HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "POST /api/admin/users/3/toggle-admin HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "GET /api/admin/users HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "GET /api/admin/works HTTP/1.1" 200 OK
INFO:     127.0.0.1:44736 - "GET /api/admin/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42014 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:42018 - "GET /api/admin/works HTTP/1.1" 200 OK
INFO:     127.0.0.1:39504 - "GET /api/admin/works/46 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39518 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:39518 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39518 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59128 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48238 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:52346 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51476 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:51476 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:41082 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:41082 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49290 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:49304 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "POST /api/works/146/favorite HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:49314 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "POST /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:49274 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:44840 - "GET /api/user/liked-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:44840 - "GET /api/user/favorite-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:44840 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:44840 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:44854 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48456 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48460 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:48472 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:48472 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:48472 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42568 - "GET /api/admin/users HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:42568 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:42568 - "GET /api/admin/works HTTP/1.1" 200 OK
INFO:     127.0.0.1:42568 - "GET /api/admin/users HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:49372 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:49372 - "GET /api/admin/works HTTP/1.1" 200 OK
INFO:     127.0.0.1:49372 - "GET /api/admin/works HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:49372 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:49372 - "GET /api/admin/users HTTP/1.1" 200 OK
INFO:     127.0.0.1:37384 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49372 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:49372 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49372 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:37398 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49372 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:47734 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39360 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57196 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:47264 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47264 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:46764 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46770 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46770 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:46770 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/46 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/46/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:56962 - "GET /api/works/46 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/59 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/59/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:56962 - "GET /api/works/59 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "POST /api/works/59/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/34 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/34/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:40946 - "GET /api/works/34 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "POST /api/works/34/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:40962 - "GET /api/works/75 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40962 - "GET /api/works/75/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:40972 - "GET /api/works/75 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40962 - "POST /api/works/75/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:54518 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:40962 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:44778 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:44782 - "GET /api/user/liked-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:44782 - "GET /api/user/favorite-works HTTP/1.1" 200 OK
INFO:     127.0.0.1:39086 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:44232 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50296 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:39220 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39220 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:39228 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39242 - "GET /api/works/110 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39242 - "GET /api/works/110/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:49302 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:37530 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44790 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:34678 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50540 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46944 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:50642 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:42698 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46270 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:37178 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:34212 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50146 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50146 - "GET /api/works/63/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:57862 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46794 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:46794 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:46794 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:33690 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33694 - "GET /api/works/46 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33694 - "GET /api/works/46/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:33694 - "GET /api/works/281 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33694 - "GET /api/works/281/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:48032 - "GET /api/works/86 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48032 - "GET /api/works/86/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:59022 - "GET /api/works/49 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59022 - "GET /api/works/49/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:59030 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59030 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:39940 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39954 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:43796 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59684 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48322 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:55570 - "GET /api/works/37 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55570 - "GET /api/works/37/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:40936 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53306 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44874 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44624 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50614 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45378 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:47900 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58216 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45004 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44964 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44964 - "GET /api/works/63/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "POST /api/works/63/like HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:33900 - "GET /api/works/63/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:44180 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44184 - "GET /api/works/63 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45540 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45540 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45540 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:45546 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35440 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58224 - "POST /api/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58224 - "GET /api/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:58224 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58240 - "GET /health HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:58246 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36364 - "GET /api/auth/me HTTP/1.1" 200 OK
Error in get_all_comments: type object 'User' has no attribute 'avatar'
INFO:     127.0.0.1:36364 - "GET /api/admin/comments HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:41454 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:57902 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56104 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:42592 - "GET /health HTTP/1.1" 200 OK
