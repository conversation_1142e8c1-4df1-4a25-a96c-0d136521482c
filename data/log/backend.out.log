Starting backend server...
Database tables created successfully
INFO:     127.0.0.1:56854 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51098 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56236 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51700 - "GET /api/works/146 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51700 - "GET /api/works/146/comments HTTP/1.1" 200 OK
INFO:     127.0.0.1:60732 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58802 - "GET /health HTTP/1.1" 200 OK
INFO:     172.19.0.1:50862 - "GET / HTTP/1.1" 200 OK
INFO:     172.19.0.1:50864 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58836 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45708 - "GET /health HTTP/1.1" 200 OK
INFO:     172.19.0.1:43060 - "GET / HTTP/1.1" 200 OK
INFO:     172.19.0.1:43066 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45722 - "GET /api/works/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45726 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:48332 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52546 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51466 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48518 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:40918 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50154 - "GET /api/auth/me HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:35828 - "GET /health HTTP/1.1" 200 OK
