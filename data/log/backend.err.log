INFO:     Will watch for changes in these directories: ['/opt/hypcup-event/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [10] using WatchFiles
INFO:     Started server process [14]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Traceback (most recent call last):
  File "/opt/hypcup-event/backend/app/api/works.py", line 103, in get_work
    raise HTTPException(status_code=404, detail="Work not found")
fastapi.exceptions.HTTPException
