#!/bin/bash

# 批量修复前端 API 路由文件的脚本
# 将所有 API_BASE_URL 配置统一为使用 getBackendApiUrl()

echo "🔧 开始修复前端 API 路由配置..."

# 定义需要修复的文件列表
API_ROUTE_FILES=(
    "frontend/app/api/auth/register/route.ts"
    "frontend/app/api/auth/me/route.ts"
    "frontend/app/api/works/[id]/route.ts"
    "frontend/app/api/works/[id]/comments/route.ts"
    "frontend/app/api/works/[id]/like/route.ts"
    "frontend/app/api/works/[id]/favorite/route.ts"
    "frontend/app/api/admin/works/route.ts"
    "frontend/app/api/admin/comments/route.ts"
    "frontend/app/api/admin/users/route.ts"
    "frontend/app/api/user/change-password/route.ts"
    "frontend/app/api/user/favorite-works/route.ts"
    "frontend/app/api/user/liked-works/route.ts"
)

# 备份原文件
echo "📁 创建备份目录..."
mkdir -p api-routes-backup
cp -r frontend/app/api/* api-routes-backup/ 2>/dev/null || true

# 修复每个文件
for file in "${API_ROUTE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "🔧 修复文件: $file"
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 使用 sed 进行替换
        sed '1,10s|// 在服务端使用内部地址，在客户端使用公网地址.*|import { getBackendApiUrl } from "@/lib/api-config"|' "$file" > "$temp_file"
        sed -i '1,10s|const API_BASE_URL = process\.env\.NODE_ENV.*|// 前端 API 路由始终使用后端内部地址\nconst API_BASE_URL = getBackendApiUrl()|' "$temp_file"
        
        # 如果文件有变化，则替换原文件
        if ! cmp -s "$file" "$temp_file"; then
            mv "$temp_file" "$file"
            echo "✅ 已修复: $file"
        else
            rm "$temp_file"
            echo "⏭️  跳过: $file (无需修复)"
        fi
    else
        echo "❌ 文件不存在: $file"
    fi
done

echo ""
echo "🎯 修复特殊文件..."

# 修复 lib/api.ts
if [ -f "frontend/lib/api.ts" ]; then
    echo "🔧 修复 frontend/lib/api.ts"
    # 这个文件需要特殊处理，因为它的逻辑更复杂
    # 暂时跳过，手动处理
    echo "⚠️  frontend/lib/api.ts 需要手动检查"
fi

# 修复上传相关的文件
if [ -f "frontend/app/api/uploads/[...path]/route.ts" ]; then
    echo "🔧 修复上传文件代理..."
    sed -i 's|const API_BASE_URL = process\.env\.NEXT_PUBLIC_API_URL.*|import { getBackendApiUrl } from "@/lib/api-config"\nconst API_BASE_URL = getBackendApiUrl().replace("/api", "")|' "frontend/app/api/uploads/[...path]/route.ts"
    echo "✅ 已修复上传文件代理"
fi

echo ""
echo "🔍 检查修复结果..."

# 检查是否还有未修复的文件
echo "检查是否还有使用旧配置的文件:"
grep -r "process\.env\.NODE_ENV.*production" frontend/app/api/ || echo "✅ 没有发现未修复的文件"

echo ""
echo "📋 修复完成！"
echo "请检查以下文件是否正确:"
echo "- frontend/lib/api-config.ts (新创建的配置文件)"
echo "- frontend/lib/api.ts (可能需要手动调整)"
echo "- 所有 frontend/app/api/ 下的路由文件"

echo ""
echo "🚀 下一步:"
echo "1. 重新构建容器: docker-compose up --build -d"
echo "2. 测试 API 调用是否正常"
echo "3. 检查前端页面是否能正常加载"
