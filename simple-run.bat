@echo off
REM Hypcup Event Platform 容器启动脚本 (Windows版本)
REM 使用Docker Compose启动前后端服务

echo 🚀 启动 Hypcup Event Platform...

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未运行，请先启动 Docker
    pause
    exit /b 1
)

REM 检查基础镜像是否存在
docker images | findstr "hypcup-event-base" >nul
if %errorlevel% neq 0 (
    echo ❌ 基础镜像 hypcup-event-base:latest 不存在
    echo 请先构建或拉取基础镜像
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建数据目录...
if not exist "data" mkdir data
if not exist "data\log" mkdir data\log
if not exist "uploads" mkdir uploads

REM 停止现有容器（如果存在）
echo 🛑 停止现有容器...
docker-compose down 2>nul

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up --build -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose ps | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo ✅ 服务启动成功！
    echo.
    echo 📊 服务信息：
    echo   前端地址: http://localhost:3000
    echo   后端API: http://localhost:8000
    echo   API文档: http://localhost:8000/docs
    echo.
    echo 📝 查看日志：
    echo   docker-compose logs -f
    echo.
    echo 🛑 停止服务：
    echo   docker-compose down
    echo.
    pause
) else (
    echo ❌ 服务启动失败，请检查日志：
    echo   docker-compose logs
    pause
    exit /b 1
)
