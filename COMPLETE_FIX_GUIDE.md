# 完整的问题修复指南

## 🎯 问题总结

根据测试结果：

1. **根路径 404**：`https://comp.uedmagazine.net/` 返回 Nginx 404 页面
2. **数据页面显示加载状态**：`https://comp.uedmagazine.net/work/63` 显示骨架屏

## 🔍 问题分析

### 1. Nginx 配置问题
- 根路径没有正确代理到前端服务 (localhost:3000)
- 可能的原因：location 规则配置错误或服务不可达

### 2. 数据获取问题
- 前端能正常渲染，但数据获取失败
- API 调用可能有问题或数据库中没有对应数据

## 🛠️ 修复步骤

### 步骤 1：检查和修复 Nginx 配置

#### 1.1 使用提供的配置文件

```bash
# 备份当前配置
cp /etc/nginx/sites-available/comp.uedmagazine.net /etc/nginx/sites-available/comp.uedmagazine.net.backup

# 使用修复后的配置
cp nginx-simple.conf /etc/nginx/sites-available/comp.uedmagazine.net

# 测试配置
nginx -t

# 重新加载配置
nginx -s reload
```

#### 1.2 关键配置检查

确保 Nginx 配置包含：

```nginx
# 后端 API 代理
location /api/ {
    proxy_pass http://127.0.0.1:8000/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 前端应用代理（必须放在最后）
location / {
    proxy_pass http://127.0.0.1:3000/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 步骤 2：检查容器服务状态

```bash
# 运行服务测试脚本
chmod +x test-nginx-config.sh
./test-nginx-config.sh

# 检查容器状态
docker compose ps

# 查看服务日志
docker compose logs hypcup-event
```

### 步骤 3：检查和修复数据

```bash
# 进入容器
docker compose exec hypcup-event bash

# 运行数据检查脚本
python3 /opt/hypcup-event/check-and-fix-data.py

# 或者在宿主机运行
python3 check-and-fix-data.py
```

### 步骤 4：测试修复结果

```bash
# 测试本地服务
curl -I http://localhost:3000/
curl -I http://localhost:8000/

# 测试通过域名访问
curl -I https://comp.uedmagazine.net/
curl -I https://comp.uedmagazine.net/api/works

# 测试特定页面
curl -I https://comp.uedmagazine.net/work/63
```

## 🔧 常见问题和解决方案

### 问题 1：Nginx 仍然返回 404

**可能原因**：
- 前端服务未启动或端口不正确
- Nginx 配置未正确加载

**解决方案**：
```bash
# 检查前端服务
curl http://localhost:3000/

# 检查 Nginx 配置
nginx -t
nginx -s reload

# 查看 Nginx 错误日志
tail -f /var/log/nginx/error.log
```

### 问题 2：前端显示加载状态

**可能原因**：
- API 调用失败
- 数据库中没有对应数据
- 前端 API 路由配置错误

**解决方案**：
```bash
# 检查 API 调用
curl http://localhost:8000/api/works/63

# 检查前端 API 代理
curl http://localhost:3000/api/works/63

# 运行数据修复脚本
python3 check-and-fix-data.py
```

### 问题 3：容器服务异常

**可能原因**：
- 容器配置错误
- 端口冲突
- 环境变量配置问题

**解决方案**：
```bash
# 重新构建容器
docker compose down
docker compose up --build -d

# 检查端口占用
netstat -tuln | grep -E "(3000|8000)"

# 检查环境变量
docker compose exec hypcup-event env | grep -E "(NODE_ENV|NEXT_PUBLIC_API_URL)"
```

## 📊 验证清单

修复完成后，请验证以下项目：

- [ ] `curl -I https://comp.uedmagazine.net/` 返回 200
- [ ] `curl -I https://comp.uedmagazine.net/api/works` 返回 200
- [ ] `curl -I https://comp.uedmagazine.net/work/63` 返回 200
- [ ] 浏览器访问根路径显示正常页面
- [ ] 浏览器访问作品页面显示具体内容（非加载状态）
- [ ] Nginx 错误日志无异常
- [ ] 容器服务运行正常

## 🚀 优化建议

### 1. 性能优化

```nginx
# 添加缓存配置
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 启用 gzip 压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### 2. 安全加固

```nginx
# 添加安全头
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
```

### 3. 监控和日志

```bash
# 设置日志轮转
logrotate /etc/logrotate.d/nginx

# 监控服务状态
systemctl status nginx
docker compose ps
```

## 📞 获取帮助

如果问题仍然存在，请提供：

1. **Nginx 配置文件内容**
2. **Nginx 错误日志**：`tail -50 /var/log/nginx/error.log`
3. **容器日志**：`docker compose logs hypcup-event`
4. **测试结果**：运行 `test-nginx-config.sh` 的输出
5. **数据库状态**：运行 `check-and-fix-data.py` 的输出

这些信息将帮助进一步诊断和解决问题。
